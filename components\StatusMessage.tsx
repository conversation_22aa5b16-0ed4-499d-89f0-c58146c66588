import React, { useEffect, useState } from 'react';
import { cn } from '../lib/utils';

interface StatusMessageProps {
  statusType: 'thinking' | 'researching' | 'structuring' | 'drafting' | 'reviewing' | 'finalizing';
  className?: string;
}

const STATUS_MESSAGES = {
  thinking: [
    "Analyzing your requirements...",
    "Understanding your needs...",
    "Processing your request..."
  ],
  researching: [
    "Researching legal requirements...",
    "Checking jurisdiction-specific laws...",
    "Reviewing relevant regulations..."
  ],
  structuring: [
    "Planning contract structure...",
    "Organizing key sections...",
    "Designing document framework..."
  ],
  drafting: [
    "Drafting key clauses...",
    "Creating contract terms...",
    "Writing legal provisions..."
  ],
  reviewing: [
    "Reviewing terms and conditions...",
    "Checking for completeness...",
    "Validating legal accuracy..."
  ],
  finalizing: [
    "Finalizing document structure...",
    "Preparing final review...",
    "Getting ready for your review..."
  ]
};

const StatusMessage: React.FC<StatusMessageProps> = ({ statusType, className }) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const messages = STATUS_MESSAGES[statusType];

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false);
      
      setTimeout(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
        setIsVisible(true);
      }, 300); // Half second fade out, then fade in
    }, 2500); // Change message every 2.5 seconds

    return () => clearInterval(interval);
  }, [messages.length]);

  return (
    <div className={cn("flex items-center gap-3 py-2", className)}>
      {/* Animated loading dots */}
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
        <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
        <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"></div>
      </div>
      
      {/* Status message with fade transition */}
      <span 
        className={cn(
          "text-sm text-zinc-600 dark:text-zinc-400 transition-opacity duration-300",
          isVisible ? "opacity-100" : "opacity-50"
        )}
      >
        {messages[currentMessageIndex]}
      </span>
    </div>
  );
};

export default StatusMessage;
