import * as React from 'react';

export enum MessageRole {
  USER = 'user',
  MODEL = 'model',
}

export interface ChatMessage {
  id: string;
  // FIX: Changed 'Message-Role' to 'role'. The hyphen caused a syntax error.
  role: MessageRole;
  content: string;
  isContract?: boolean;
  initialReply?: string;
  result?: string;
  hasTwoSections?: boolean;
  // Enhanced conversational properties
  messageType?: 'conversation' | 'status' | 'question' | 'progress' | 'confirmation' | 'summary';
  statusType?: 'thinking' | 'researching' | 'structuring' | 'drafting' | 'reviewing' | 'finalizing';
  stage?: string;
  suggestedResponses?: string[];
  requiresConfirmation?: boolean;
  progressPercentage?: number;
  isTyping?: boolean;
  conversationData?: Record<string, any>;
}

export interface DocumentVersion {
  versionId: string;
  content: string;
  savedAt: string;
  version: number;
}

export interface DocumentMetadata {
  wordCount: number;
  lastModified: string;
  version: number;
}

export type Permission = 'view' | 'edit';

export interface Collaborator {
    email: string;
    permission: Permission;
    avatarUrl?: string;
}

export interface Comment {
  id: string;
  authorEmail: string;
  content: string;
  createdAt: string;
}

export interface CommentThread {
  id: string;
  textSelection: string;
  comments: Comment[];
  isResolved: boolean;
  position?: { top: number; left: number }; // For rendering hints, not persisted
}

// Enhanced conversation state management
export interface ConversationState {
  currentStage: ConversationStage;
  collectedData: Record<string, any>;
  contractType?: string;
  parties?: string[];
  jurisdiction?: string;
  keyTerms?: Record<string, any>;
  requirements?: RequirementItem[];
  isComplete?: boolean;
  lastUpdated?: Date;
}

export interface RequirementItem {
  id: string;
  question: string;
  answer?: string;
  isRequired: boolean;
  type: 'text' | 'select' | 'multiselect' | 'boolean';
  options?: string[];
  validated?: boolean;
}

export enum ConversationStage {
  INITIAL = 'initial',
  ANALYZING_REQUEST = 'analyzing_request',
  GATHERING_REQUIREMENTS = 'gathering_requirements',
  CONFIRMING_DETAILS = 'confirming_details',
  PLANNING_STRUCTURE = 'planning_structure',
  GENERATING_OUTLINE = 'generating_outline',
  GENERATING_CONTRACT = 'generating_contract',
  REVIEW_READY = 'review_ready',
  COMPLETED = 'completed'
}

export interface ConversationProgress {
  stage: ConversationStage;
  stageLabel: string;
  percentage: number;
  statusMessage: string;
  isActive: boolean;
  isCompleted: boolean;
}

export interface Notification {
  id: string;
  type: 'comment' | 'share' | 'signature' | 'team' | 'marketing' | 'approval';
  message: string;
  documentId?: string;
  createdAt: string;
  isRead: boolean;
}

export type AssistanceType = 'Draft Contract' | 'Suggest Clause' | 'Legal Assistance' | 'General Legal Query';

export type ToneType = 'Professional' | 'Casual' | 'Friendly' | 'Formal';

export type DocumentStatus = 'draft' | 'in-review' | 'approved' | 'out-for-signature' | 'completed' | 'archived';

export interface Signature {
    id: string;
    email: string;
    role: string;
    status: 'pending' | 'signed';
    signedAt?: string;
    token: string;
}

export interface KeyDate {
    event: string;
    date: string; // ISO 8601 format
}

export interface Approver {
    email: string;
    status: 'pending' | 'approved' | 'changes-requested';
    comments?: string;
    respondedAt?: string;
}

export interface Obligation {
  id: string;
  documentId: string;
  description: string;
  dueDate: string; // ISO 8601 format
  ownerEmail?: string;
  status: 'pending' | 'completed' | 'overdue';
}

export interface Client {
  id: string;
  name: string;
  type: 'Company' | 'Individual';
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  createdAt: string;
}

export interface ClientAsset {
  id: string;
  clientId: string;
  name: string;
  url: string;
  createdAt: string;
}

export interface Document {
  id: string;
  name: string;
  content: string; // Represents the latest content
  createdAt: string;
  updatedAt: string;
  folderId?: string | null;
  clientId?: string | null;
  metadata?: DocumentMetadata;
  versions: DocumentVersion[];
  collaborators: Collaborator[];
  commentThreads: CommentThread[];
  status: DocumentStatus;
  signatures: Signature[];
  keyDates?: KeyDate[];
  activityLogs?: ActivityLog[];
  approvers?: Approver[];
  obligations?: Obligation[];
  value?: number; // For workflow conditions
  tags?: string[]; // For workflow actions
}

export interface DocumentsPaginationState {
  limit: number;
  offset: number;
  total: number | null;
  hasMore: boolean;
  loading: boolean;
}

export interface Folder {
  id: string;
  name: string;
}

export interface Clause {
    id: string;
    title: string;
    content: string; // HTML content
    tags: string[];
    createdAt: string;
}

export type DashboardView = 'dashboard' | 'generate' | 'history' | 'subscription' | 'settings' | 'templates' | 'documentDetail' | 'clientDetail' | 'lifecycle' | 'clauseLibrary' | 'team' | 'notifications' | 'analysis' | 'help' | 'obligations' | 'workflows' | 'clients' | 'integrations';
export type AdminView = 'analytics' | 'plans' | 'cms' | 'settings' | 'profile' | 'billing';

export interface Template {
    id: string;
    title: string;
    description: string;
    category: string;
    prompt: string;
    requiredPlan: 'Registered User' | 'Premium';
}

export interface CustomTemplate {
    id: string;
    name: string;
    content: string;
    createdAt: string;
}

export interface AnalysisFinding {
    severity: 'High Priority' | 'Suggestion' | 'Information';
    description: string;
}

export interface AnalysisResult {
    summary: string;
    findings: AnalysisFinding[];
}


export interface SuggestedClause {
    title: string;
    description: string;
    content: string;
}

export interface ActivityLog {
    id: string;
    type: 'create' | 'edit' | 'share' | 'comment' | 'view' | 'signature' | 'revert' | 'team' | 'approval';
    userEmail: string;
    details: string;
    timestamp: string;
}

export type TeamMemberRole = 'Admin' | 'Member';

export interface TeamMember {
    userId: string;
    email: string;
    role: TeamMemberRole;
    avatarUrl?: string;
}

export interface ApiKey {
    id: string;
    name: string;
    key: string;
    createdAt: string;
}

export interface SsoConfig {
    enabled: boolean;
    idpUrl: string;
    entityId: string;
    certificate: string;
}

export type WorkflowTriggerType = 'document-created' | 'approval-requested' | 'status-changed';

export type WorkflowNodeType = 'trigger' | 'condition' | 'approval' | 'notification' | 'delay' | 'update-field' | 'wait-for-signature' | 'add-collaborator' | 'move-to-folder' | 'add-tag';

export interface WorkflowNodeData {
  label: string;
  triggerType?: WorkflowTriggerType;
  // Condition-specific
  conditionField?: 'value' | 'status' | 'folderId' | 'name' | 'createdAt' | 'clientId';
  conditionOperator?: '>' | '<' | '==' | 'is' | 'is not' | 'contains' | 'starts with' | 'ends with' | 'before' | 'after';
  conditionValue?: number | string | null;
  // Approval-specific
  approverEmail?: string;
  // Notification-specific
  notificationMessage?: string;
  notificationRecipient?: string;
  // Delay-specific
  delayDays?: number;
  // Update Field-specific
  updateField?: 'status';
  updateValue?: DocumentStatus;
  // Add Collaborator-specific
  collaboratorEmail?: string;
  collaboratorPermission?: Permission;
  // Move to Folder-specific
  targetFolderId?: string;
  // Add Tag-specific
  tagName?: string;
}

export interface WorkflowNode {
  id: string;
  type: WorkflowNodeType;
  position: { x: number; y: number };
  data: WorkflowNodeData;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  sourceHandle?: string; // e.g., 'true' or 'false' for conditions
  target: string;
  targetHandle?: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  status: 'active' | 'inactive';
}

export interface WorkflowInstance {
    id: string;
    templateId: string;
    documentId: string;
    status: 'running' | 'completed' | 'failed' | 'waiting';
    currentNodeId: string;
    createdAt: string;
    resumeAt?: string; // For delay nodes
}

export interface IntegrationConfig {
  autoCreateCustomer: boolean;
  invoiceTriggerStatus: DocumentStatus;
  defaultInvoiceItem: string;
}

export interface Integration {
  id: string;
  name: string;
  description: string;
  status: 'connected' | 'disconnected';
  syncStatus: 'syncing' | 'synced' | 'error' | null;
  config?: IntegrationConfig;
}

export interface Team {
    id: string;
    name: string;
    ownerId: string;
    members: TeamMember[];
    apiKeys?: ApiKey[];
    ssoConfig?: SsoConfig;
    status: 'active' | 'suspended';
    workflows?: WorkflowTemplate[];
}

// ===============================================
// NEW: Connector-based Integration Architecture Types
// ===============================================

export type AuthType = 'oauth2' | 'apiKey';
export type TriggerType = 'polling' | 'webhook';

export interface AuthField {
  key: string;
  label: string;
  type: 'text' | 'password';
  helpText?: string;
}

export interface AuthMethod {
  type: AuthType;
  fields?: AuthField[]; // For API Key auth
  // OAuth2 specific fields would go here, e.g., authUrl, tokenUrl, scopes
}

export interface ConnectorField {
  key: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'datetime';
  required?: boolean;
}

export interface Trigger {
  key: string;
  name: string;
  description: string;
  type: TriggerType;
  operation: {
    // In a real app, this would be a reference to the function that performs the poll or handles the webhook
    perform: (...args: unknown[]) => unknown;
    outputFields: ConnectorField[];
  };
}

export interface Action {
  key: string;
  name: string;
  description: string;
  operation: {
    perform: (...args: unknown[]) => unknown;
    inputFields: ConnectorField[];
  };
}

export interface Connector {
  id: string;
  name: string;
  description: string;
  icon: React.FC<{className?: string}>;
  auth: AuthMethod;
  triggers?: Trigger[];
  actions?: Action[];
}

export interface UserConnection {
    id: string;
    connectorId: string;
    userId: string;
    // In a real app, credentials would reference a secure vault, not be stored directly
    credentials: Record<string, unknown>;
    createdAt: string;
}

export interface Flow {
    id: string;
    name: string;
    userId: string;
    trigger: {
        connectionId: string;
        triggerKey: string;
    };
    action: {
        connectionId: string;
        actionKey: string;
    };
    fieldMapping: Record<string, string>; // Maps action input field key to trigger output field key
    status: 'active' | 'inactive';
    createdAt: string;
}

export interface FlowRun {
    id: string;
    flowId: string;
    userId: string;
    status: 'success' | 'failed' | 'running';
    startedAt: string;
    finishedAt?: string;
    triggerData?: Record<string, unknown>;
    actionData?: Record<string, unknown>;
    error?: string | null;
}


// ===============================================
// End of New Integration Types
// ===============================================

export type Theme = 'light' | 'dark' | 'system';

export interface NotificationPreferences {
    comments: boolean;
    shares: boolean;
    signatures: boolean;
    team: boolean;
    marketing: boolean;
}

export interface User {
  id: string; // Add user ID for team management
  email: string;
  password: string;
  isVerified: boolean;
  isSuperAdmin?: boolean;
  status: 'active' | 'suspended';
  createdAt?: string;
  planExpiryDate?: string;
  verificationToken?: string;
  passwordResetToken?: string;
  resetTokenExpiry?: number;
  documents: Document[];
  folders: Folder[];
  clients?: Client[];
  clauses?: Clause[];
  customTemplates?: CustomTemplate[];
  notifications?: Notification[];
  quotaUsed: number;
  quotaTotal: number;
  planName: string;
  name?: string;
  username?: string;
  avatarUrl?: string;
  teamId?: string | null;
  theme?: Theme;
  notificationPreferences?: NotificationPreferences;
  jobTitle?: string;
  company?: string;
  bio?: string;
  websiteUrl?: string;
  linkedinUrl?: string;
  // Fields to simulate data from a payment provider like Stripe
  subscriptionId?: string | null;
  customerId?: string | null;
  subscriptionStatus?: 'active' | 'canceled' | null;
  // New integration state
  connections?: UserConnection[];
  flows?: Flow[];
  flowRuns?: FlowRun[];
}

// Types for External Document Analysis
export interface AnalysisClause {
    title: string;
    summary: string;
}

export interface AnalysisRisk {
    severity: 'High' | 'Medium' | 'Low';
    description: string;
}

export interface ExternalAnalysisResult {
    summary: string;
    documentType: string;
    keyClauses: AnalysisClause[];
    risks: AnalysisRisk[];
    suggestions: string[];
}

// Types for Help Center Content
export type HelpContentBlock =
  | { type: 'text'; value: string }
  | { type: 'screenshot'; component: React.FC<Record<string, unknown>> };

export interface HelpTopic {
  id: string;
  title: string;
  content: string | HelpContentBlock[];
}

export interface HelpCategory {
  category: string;
  icon: React.FC<{className?: string}>;
  topics: HelpTopic[];
}

export type SearchResult = {
  id: string;
  title: string;
  type: 'document' | 'folder' | 'clause' | 'help';
  context?: string;
};

export interface PricingPlan {
  id: string;
  name: string;
  price: string;
  priceDetail?: string;
  features: string[];
  cta: string;
  isFeatured: boolean;
  sortOrder?: number;
}

export interface Testimonial {
  id: string;
  quote: string;
  name: string;
  title?: string;
  avatar?: string;
  audiences?: string[];
  highlight?: boolean;
}

export type AdminSearchResultType = 'user' | 'plan' | 'template' | 'page';

export interface AdminSearchResult {
  id: string;
  title: string;
  type: AdminSearchResultType;
  context?: string;
}

export interface StripeConfig {
    livePublishableKey: string;
    liveSecretKey: string;
    testPublishableKey: string;
    testSecretKey: string;
    webhookSecret: string;
    isLiveMode: boolean;
}
