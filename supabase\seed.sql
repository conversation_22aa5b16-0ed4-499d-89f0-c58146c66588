-- Optional seed script for local development.
-- Intentionally minimal to avoid failing `supabase db push` when seeds are enabled.

-- Safe, idempotent seeds using WHERE NOT EXISTS guards

-- Admin user (email confirmation = false)
-- Creates an admin user in auth.users and corresponding profile with is_admin = true
do $$
declare
  _uid uuid;
begin
  select id into _uid from auth.users where email = '<EMAIL>' limit 1;
  if _uid is null then
    with new_user as (
      insert into auth.users (
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        raw_app_meta_data,
        raw_user_meta_data,
        aud,
        role
      ) values (
        gen_random_uuid(),
        '<EMAIL>',
        crypt('superadmin123', gen_salt('bf')),
        null,
        '{"provider":"email","providers":["email"]}'::jsonb,
        '{}'::jsonb,
        'authenticated',
        'authenticated'
      )
      returning id
    )
    insert into public.profiles (id, name, username, is_admin, plan_name, status)
    select id, 'Super Admin', 'admin', true, 'Enterprise', 'active' from new_user
    on conflict (id) do update set is_admin = excluded.is_admin;
  else
    -- Ensure the existing profile is marked as admin
    insert into public.profiles (id, is_admin, name, username, plan_name, status)
    values (_uid, true, 'Super Admin', 'admin', 'Enterprise', 'active')
    on conflict (id) do update set is_admin = excluded.is_admin;
  end if;
end $$;

-- Templates
insert into public.templates (title, description, category, prompt, required_plan)
select 'Mutual NDA', 'Professional two-way confidentiality agreement for business partnerships and collaborations', 'Contracts', 'Draft a comprehensive mutual non-disclosure agreement between two parties. Include detailed confidentiality obligations, permitted uses and disclosures, exclusions for publicly available information, term duration, return of confidential materials, remedies for breach including injunctive relief, and governing law provisions. Ensure the agreement protects both parties equally and includes standard legal protections.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Mutual NDA');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Freelance Services Agreement', 'Comprehensive independent contractor agreement with IP protection and clear deliverables', 'Agreements', 'Draft a detailed freelance services agreement that clearly defines the scope of work, project deliverables, and timeline. Include comprehensive payment terms with milestone-based payments, late payment penalties, and expense reimbursement. Address intellectual property ownership, work-for-hire provisions, and confidentiality obligations. Include termination clauses for both convenience and cause, dispute resolution mechanisms, and independent contractor status confirmation. Add provisions for revisions, acceptance criteria, and liability limitations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Freelance Services Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Last Will and Testament', 'Legally compliant individual will with executor and beneficiary provisions', 'Wills', 'Draft a comprehensive last will and testament including appointment of executor with powers and duties, specific bequests to named beneficiaries, residual estate distribution, guardianship provisions for minor children if applicable, and revocation of prior wills. Include attestation clause for witnesses, self-proving affidavit where permitted, and jurisdiction-neutral language that can be adapted to local requirements.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Last Will and Testament');

-- Healthcare (15)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Business Associate Agreement (HIPAA)', 'HIPAA-compliant agreement for protected health information handling and processing', 'Healthcare', 'Draft a comprehensive HIPAA-compliant Business Associate Agreement between a Covered Entity and a Business Associate. Include detailed definitions of PHI and business associate services, specific permitted uses and disclosures with limitations, required administrative, physical, and technical safeguards, breach notification procedures with specific timelines and reporting requirements, subcontractor management and flow-down obligations, audit rights and compliance monitoring, termination procedures with data return or destruction requirements, and indemnification provisions. Ensure compliance with current HIPAA regulations and include provisions for regulatory updates.', 'Premium'
where not exists (select 1 from public.templates where title = 'Business Associate Agreement (HIPAA)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Informed Consent Form', 'Patient consent form for medical procedures with comprehensive risk disclosure', 'Healthcare', 'Create a detailed informed consent form for medical procedures using clear, patient-friendly language. Include comprehensive description of the proposed procedure, material risks and potential complications, expected benefits and likelihood of success, alternative treatment options including no treatment, post-procedure care instructions and restrictions, patient rights including right to withdraw consent, emergency contact procedures, and proper signature blocks with date and witness fields. Add placeholders for specific procedure details, provider information, and patient identification.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Informed Consent Form');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Telehealth Services Agreement', 'Comprehensive telemedicine service agreement with privacy and technology requirements', 'Healthcare', 'Draft a detailed telehealth services agreement establishing the provider-patient relationship for remote care. Include patient eligibility criteria and limitations, technology requirements and technical support, privacy and security measures for video consultations, emergency care limitations and local emergency procedures, informed consent for remote care, fee structure and insurance coverage, prescription and medication management policies, medical record access and portability, termination procedures, and liability limitations. Use jurisdiction-neutral language with clear disclaimers about emergency care limitations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Telehealth Services Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Medical Director Agreement', 'Executive physician services agreement with compliance and regulatory provisions', 'Healthcare', 'Prepare a comprehensive Medical Director Agreement covering administrative and clinical duties, time commitment and scheduling requirements, reporting relationships and committee participation, compensation structure including base salary and performance incentives, regulatory compliance obligations including anti-kickback and Stark Law compliance, medical staff credentialing and privileging requirements, professional liability insurance coverage, non-solicitation and non-compete provisions where legally enforceable, confidentiality and proprietary information protection, termination procedures for cause and without cause, and post-termination obligations. Include representations regarding fair market value and commercial reasonableness.', 'Premium'
where not exists (select 1 from public.templates where title = 'Medical Director Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Clinical Trial NDA', 'Specialized confidentiality agreement for clinical research and trial data protection', 'Healthcare', 'Draft a comprehensive mutual non-disclosure agreement specifically tailored for clinical trials and research collaborations. Address protocol confidentiality and proprietary research methodologies, patient data and subject information protection, intellectual property rights and invention ownership, publication rights and embargo periods, regulatory disclosure obligations to FDA and other authorities, residual knowledge and know-how provisions, survival of obligations post-termination, and equitable relief provisions for breach. Include specific provisions for multi-site trials and international research collaborations.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Clinical Trial NDA');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Patient Privacy Notice', 'HIPAA-compliant Notice of Privacy Practices for healthcare providers', 'Healthcare', 'Write a comprehensive Notice of Privacy Practices fully compliant with HIPAA requirements. Explain permitted uses and disclosures for treatment, payment, and healthcare operations, patient rights including access, amendment, and restriction requests, complaint procedures and contact information for privacy officer, authorization requirements and revocation procedures, minimum necessary standards, and breach notification procedures. Use plain language with clear headings and organize information for patient accessibility and understanding.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Patient Privacy Notice');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Healthcare Services Agreement', 'Professional services agreement between healthcare providers and medical facilities', 'Healthcare', 'Draft a comprehensive healthcare services agreement between an independent provider and a healthcare facility. Include detailed scope of services and clinical responsibilities, staffing requirements and coverage obligations, regulatory compliance including anti-kickback and Stark Law provisions, billing and collection procedures, quality assurance and peer review requirements, credentialing and privileging maintenance, professional liability insurance requirements, audit rights and compliance monitoring, indemnification provisions, and termination procedures with patient care transition requirements. Ensure fair market value compliance and commercial reasonableness.', 'Premium'
where not exists (select 1 from public.templates where title = 'Healthcare Services Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Data Use Agreement (HIPAA Limited Data Set)', 'HIPAA-compliant agreement for limited data set research and analysis', 'Healthcare', 'Create a comprehensive HIPAA Data Use Agreement for Limited Data Sets used in research and analysis. Specify permitted uses and purposes with clear limitations, required administrative, physical, and technical safeguards, prohibition on re-identification attempts, restrictions on redisclosure to third parties, reporting requirements for violations and breaches, subcontractor and collaborator obligations, data return or destruction procedures upon termination, and compliance monitoring and audit rights. Provide detailed placeholders for data categories, research purposes, and institutional review board approvals.', 'Premium'
where not exists (select 1 from public.templates where title = 'Data Use Agreement (HIPAA Limited Data Set)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'IRB Authorization Letter', 'Institutional Review Board authorization', 'Healthcare', 'Draft an IRB authorization/approval letter template including protocol title, principal investigator, approval date, conditions, reporting obligations, and expiration/continuing review info. Use formal institutional tone.', 'Registered User'
where not exists (select 1 from public.templates where title = 'IRB Authorization Letter');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Physician Employment Agreement', 'Physician hire terms', 'Healthcare', 'Create a physician employment agreement covering duties, call schedule, compensation/bonuses, malpractice tail, credentialing, non-compete (jurisdiction dependent), non-solicit, compliance, and termination. Include fair market value representation.', 'Premium'
where not exists (select 1 from public.templates where title = 'Physician Employment Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Business Continuity Plan (Clinic)', 'BCP for clinical operations', 'Healthcare', 'Draft a Business Continuity Plan for a small clinic. Include risk assessment, critical systems, communication tree, backup procedures, alternative sites, and recovery steps. Provide checklists and roles.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Business Continuity Plan (Clinic)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Medical Device Evaluation Agreement', 'Device trial with provider', 'Healthcare', 'Prepare an agreement for evaluation of a medical device in a clinical setting. Include safety responsibilities, training, data collection ownership, no-charge loan terms, IP, confidentiality, and disclaimers.', 'Premium'
where not exists (select 1 from public.templates where title = 'Medical Device Evaluation Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Patient Financial Responsibility Form', 'Billing acknowledgement', 'Healthcare', 'Create a patient financial responsibility acknowledgement describing coverage verification limits, copays, deductibles, non-covered services, billing disputes, and payment options. Use accessible language and signature lines.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Patient Financial Responsibility Form');
insert into public.templates (title, description, category, prompt, required_plan)
select 'De-Identification Attestation', 'HIPAA de-identification statement', 'Healthcare', 'Draft an attestation that data meets HIPAA de-identification standards (Safe Harbor or Expert Determination). Include scope, methods, residual risk statement, and signer qualifications.', 'Premium'
where not exists (select 1 from public.templates where title = 'De-Identification Attestation');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Clinic Vendor Agreement', 'Vendor services to clinic', 'Healthcare', 'Draft a vendor agreement for a medical clinic covering services, SLAs, access to PHI limits, security obligations, incident reporting, insurance, indemnity, and termination assistance. Include a BAA if needed.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Clinic Vendor Agreement');

-- Technology / SaaS (15)
insert into public.templates (title, description, category, prompt, required_plan)
select 'SaaS Master Subscription Agreement', 'MSA for SaaS product', 'Technology', 'Draft a SaaS MSA including license/rights, acceptable use, availability SLAs, support, data security, privacy, IP, feedback license, warranties, indemnities, liability caps, pricing, renewals, and termination.', 'Premium'
where not exists (select 1 from public.templates where title = 'SaaS Master Subscription Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Service Level Agreement (SLA)', 'Availability and support metrics', 'Technology', 'Create an SLA for a cloud service. Define uptime, maintenance windows, response/restore times, exclusions, service credits, claim process, and reporting. Be precise and measurable.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Service Level Agreement (SLA)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Data Processing Addendum (GDPR)', 'Controller-Processor DPA', 'Technology', 'Draft a GDPR-compliant DPA covering subject matter, duration, nature, purpose, data categories, data subject rights support, security measures, sub-processors, transfers, audits, breach notice, and deletion/return.', 'Premium'
where not exists (select 1 from public.templates where title = 'Data Processing Addendum (GDPR)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Acceptable Use Policy (AUP)', 'User behavior policy', 'Technology', 'Write an AUP for a SaaS platform. Prohibit abuse, security violations, illegal content, spamming, scraping, and disruptions. Describe enforcement, reporting, and escalation.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Acceptable Use Policy (AUP)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Mutual NDA (Tech)', 'NDA for tech collaboration', 'Technology', 'Create a mutual NDA tailored to software and product roadmaps, source code, APIs, security posture, and customer lists. Include residuals, exclusions, and export control.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Mutual NDA (Tech)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Pilot Agreement (SaaS)', 'Limited pilot terms', 'Technology', 'Draft a pilot agreement with limited license, sandbox use, no production warranty, feedback license, data export, confidentiality, and conversion mechanics with pricing triggers.', 'Premium'
where not exists (select 1 from public.templates where title = 'Pilot Agreement (SaaS)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Reseller Agreement', 'Channel resale terms', 'Technology', 'Prepare a reseller agreement covering territory, exclusivity, branding, ordering, pricing, support responsibilities, training, compliance, and indemnity. Include end-user agreement pass-through.', 'Premium'
where not exists (select 1 from public.templates where title = 'Reseller Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Professional Services SOW', 'Implementation scope of work', 'Technology', 'Create a Statement of Work template for implementation services. Include scope, milestones, deliverables, assumptions, acceptance criteria, change control, and fees/expenses.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Professional Services SOW');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Open Source Compliance Policy', 'Use and contribution policy', 'Technology', 'Draft a policy for open-source use and contributions. Include license review, approval workflow, attribution, obligations, vulnerability handling, and contribution guidelines.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Open Source Compliance Policy');
insert into public.templates (title, description, category, prompt, required_plan)
select 'API License Agreement', 'Terms for API usage', 'Technology', 'Write an API license agreement detailing key restrictions, rate limits, keys/rotation, data caching/storage, attribution, security, and termination. Provide clear definitions and examples.', 'Premium'
where not exists (select 1 from public.templates where title = 'API License Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Security Incident Response Plan', 'IR plan for SaaS', 'Technology', 'Create a security incident response plan with roles, severity levels, triage, containment, eradication, recovery, postmortems, and communication templates. Reference industry standards.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Security Incident Response Plan');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Subprocessor Disclosure', 'List of subprocessors', 'Technology', 'Generate a subprocessor disclosure template showing services, locations, and purposes. Add change notification and objection process consistent with GDPR.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Subprocessor Disclosure');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Penetration Testing Rules of Engagement', 'Pentest scope and rules', 'Technology', 'Draft rules of engagement for third-party pentesting: scope, targets, methods, scheduling, data handling, reporting, and legal indemnities. Include sensitive systems exclusions.', 'Premium'
where not exists (select 1 from public.templates where title = 'Penetration Testing Rules of Engagement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Data Retention & Deletion Policy', 'Lifecycle and deletion SLAs', 'Technology', 'Write a data retention/deletion policy specifying categories, retention periods, legal holds, backups, and deletion SLAs including user-export pathways. Keep concise tables.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Data Retention & Deletion Policy');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Customer DPA Questionnaire', 'Customer security questionnaire', 'Technology', 'Create a structured questionnaire to collect controller-required details for a DPA: processing activities, locations, subprocessors, TOMs, certifications, and contact points.', 'Premium'
where not exists (select 1 from public.templates where title = 'Customer DPA Questionnaire');

-- Real Estate (15)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Residential Lease Agreement', 'Fixed-term residential lease', 'Real Estate', 'Draft a residential lease with term, rent, deposit, maintenance, utilities, quiet enjoyment, entry rights, assignment/sublet, default/remedies, and local law disclosures placeholders.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Residential Lease Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Commercial Lease Agreement', 'Office/retail/industrial lease', 'Real Estate', 'Create a commercial lease including permitted use, build-out, CAM charges, taxes, insurance, indemnity, compliance, assignment, default, and restoration.', 'Premium'
where not exists (select 1 from public.templates where title = 'Commercial Lease Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Purchase & Sale Agreement (Residential)', 'Home purchase terms', 'Real Estate', 'Draft a residential purchase agreement with price, contingencies, inspections, disclosures, title, escrow, prorations, closing, and remedies. Add placeholders for dates and addenda.', 'Premium'
where not exists (select 1 from public.templates where title = 'Purchase & Sale Agreement (Residential)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Real Estate NDA', 'Confidentiality for property info', 'Real Estate', 'Write an NDA for property marketing materials and financials shared during diligence. Include non-circumvention, no-solicit, and return/destroy of materials.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Real Estate NDA');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Property Management Agreement', 'Landlord-agent management terms', 'Real Estate', 'Prepare a property management agreement detailing scope (rent collection, maintenance), fees, bank accounts, authority limits, reporting, insurance, compliance, and termination.', 'Premium'
where not exists (select 1 from public.templates where title = 'Property Management Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Short-Term Rental Agreement', 'Vacation/short-term rental terms', 'Real Estate', 'Create a short-term rental agreement with house rules, occupancy limits, deposits, cancellations, liability waivers, and local ordinance compliance.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Short-Term Rental Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Brokerage Listing Agreement', 'Exclusive right to sell', 'Real Estate', 'Draft a listing agreement covering term, commission, exclusions, marketing, disclosures, dual agency, and early termination. Include regulatory compliance placeholders.', 'Premium'
where not exists (select 1 from public.templates where title = 'Brokerage Listing Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Letter of Intent (LOI) - Lease', 'Non-binding lease LOI', 'Real Estate', 'Write a non-binding LOI summarizing key lease terms: premises, term, rent, TI allowance, options, and exclusivity window. Include confidentiality and good-faith negotiation.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Letter of Intent (LOI) - Lease');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Rent Increase Notice', 'Landlord rent increase letter', 'Real Estate', 'Prepare a compliant rent increase notice with effective date, new rent, basis, and options. Include placeholders for jurisdictional notice periods.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Rent Increase Notice');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Eviction Notice (For Cause)', 'Notice to cure or quit', 'Real Estate', 'Draft a cause-based notice outlining violations, cure periods, consequences, and service method. Keep neutral and include statutory references placeholders.', 'Premium'
where not exists (select 1 from public.templates where title = 'Eviction Notice (For Cause)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Lease Amendment', 'Modify lease terms', 'Real Estate', 'Create a lease amendment template to change term, rent, occupants, or rules. Reference original lease, effective date, and confirmation of unchanged terms.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Lease Amendment');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Tenant Estoppel Certificate', 'Tenant confirmations for lenders/buyers', 'Real Estate', 'Write an estoppel certificate confirming lease in force, defaults, deposits, options, and offsets. Provide signature blocks and attach copy of lease if needed.', 'Premium'
where not exists (select 1 from public.templates where title = 'Tenant Estoppel Certificate');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Move-In/Move-Out Checklist', 'Condition checklist for rentals', 'Real Estate', 'Provide a checklist to record unit condition at move-in and move-out including photos, meter readings, keys, and damages. Include signature fields.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Move-In/Move-Out Checklist');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Commercial Sublease Agreement', 'Sublease for commercial space', 'Real Estate', 'Draft a commercial sublease addressing landlord consent, permitted use, rent flow-through, improvements, liability, and remedies. Incorporate master lease terms by reference.', 'Premium'
where not exists (select 1 from public.templates where title = 'Commercial Sublease Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Security Deposit Return Letter', 'Deposit disposition notice', 'Real Estate', 'Create a letter itemizing deposit deductions with supporting basis and payment details. Include deadlines and dispute process placeholders per local law.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Security Deposit Return Letter');

-- Business Contracts (12)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Master Service Agreement', 'Comprehensive framework agreement for ongoing business services', 'Business Contracts', 'Draft a comprehensive Master Service Agreement establishing the framework for ongoing business services. Include detailed service descriptions and performance standards, statement of work procedures and change management, payment terms with milestone-based billing, intellectual property ownership and licensing provisions, confidentiality and data protection requirements, liability limitations and indemnification clauses, service level agreements with remedies for non-performance, termination procedures for convenience and cause, dispute resolution mechanisms including mediation and arbitration, and governing law provisions. Ensure scalability for multiple projects and service types.', 'Premium'
where not exists (select 1 from public.templates where title = 'Master Service Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Partnership Agreement', 'Legal partnership formation agreement with profit sharing and governance', 'Business Contracts', 'Create a comprehensive partnership agreement for business formation. Include partner contributions of capital, property, and services, profit and loss distribution mechanisms, management structure and decision-making authority, partner duties and restrictions on outside activities, admission of new partners and transfer of interests, dissolution procedures and asset distribution, accounting methods and financial reporting requirements, dispute resolution procedures, and buy-sell provisions for partner withdrawal or death. Address tax elections and regulatory compliance requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Partnership Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Vendor Supply Agreement', 'Long-term supplier contract with quality standards and delivery terms', 'Business Contracts', 'Draft a comprehensive vendor supply agreement for goods or materials. Include detailed product specifications and quality standards, delivery schedules and logistics requirements, pricing mechanisms with volume discounts, payment terms and credit arrangements, quality control and inspection procedures, warranty provisions and defect remedies, force majeure and supply disruption procedures, intellectual property rights and proprietary information protection, termination clauses and transition assistance, and regulatory compliance requirements. Include provisions for supply chain transparency and sustainability standards.', 'Premium'
where not exists (select 1 from public.templates where title = 'Vendor Supply Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Consulting Agreement', 'Professional consulting services contract with deliverables and confidentiality', 'Business Contracts', 'Create a detailed consulting agreement for professional advisory services. Include comprehensive scope of work and project deliverables, consultant qualifications and expertise requirements, time commitment and availability expectations, fee structure with expense reimbursement policies, intellectual property ownership and work-for-hire provisions, confidentiality obligations and non-disclosure requirements, independent contractor status confirmation, liability limitations and professional indemnity, termination procedures with notice requirements, and non-solicitation provisions. Address conflict of interest policies and regulatory compliance.', 'Premium'
where not exists (select 1 from public.templates where title = 'Consulting Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Joint Venture Agreement', 'Strategic business collaboration with shared risks and rewards', 'Business Contracts', 'Draft a comprehensive joint venture agreement for strategic business collaboration. Include venture purpose and business objectives, partner contributions and resource allocation, governance structure with management committee, profit and loss sharing arrangements, intellectual property development and ownership, confidentiality and proprietary information protection, exclusivity provisions and territorial restrictions, performance metrics and milestone requirements, dispute resolution mechanisms, dissolution procedures and asset distribution, and regulatory compliance obligations. Address tax treatment and accounting methods.', 'Premium'
where not exists (select 1 from public.templates where title = 'Joint Venture Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Franchise Agreement', 'Franchising contract with brand standards and territorial rights', 'Business Contracts', 'Create a comprehensive franchise agreement establishing franchisee rights and obligations. Include franchise territory and exclusivity provisions, initial and ongoing franchise fees, brand standards and operational requirements, training and support obligations, marketing and advertising requirements, quality control and inspection rights, intellectual property licensing and protection, supplier and vendor requirements, financial reporting and audit rights, transfer and assignment restrictions, termination procedures and post-termination obligations, and dispute resolution mechanisms. Ensure compliance with franchise disclosure requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Franchise Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Distribution Agreement', 'Product distribution contract with territorial rights and sales targets', 'Business Contracts', 'Draft a comprehensive distribution agreement for product sales and marketing. Include territorial boundaries and exclusivity provisions, product lines and distribution rights, sales targets and performance requirements, pricing policies and discount structures, marketing and promotional obligations, inventory management and ordering procedures, customer service and technical support responsibilities, intellectual property licensing and brand usage rights, termination procedures and inventory buy-back provisions, and regulatory compliance requirements. Address online sales policies and channel conflict resolution.', 'Premium'
where not exists (select 1 from public.templates where title = 'Distribution Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Manufacturing Agreement', 'Contract manufacturing services with quality control and IP protection', 'Business Contracts', 'Create a detailed manufacturing agreement for contract production services. Include product specifications and quality standards, manufacturing processes and capacity allocation, raw material sourcing and inventory management, quality control and testing procedures, delivery schedules and logistics coordination, pricing mechanisms and cost adjustments, intellectual property protection and confidentiality, regulatory compliance and certification requirements, warranty provisions and defect remedies, force majeure and supply chain disruption procedures, and termination procedures with transition assistance. Include provisions for continuous improvement and technology updates.', 'Premium'
where not exists (select 1 from public.templates where title = 'Manufacturing Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Strategic Alliance Agreement', 'Business partnership for market expansion and resource sharing', 'Business Contracts', 'Draft a comprehensive strategic alliance agreement for business collaboration. Include alliance objectives and strategic goals, partner roles and responsibilities, resource sharing and contribution requirements, market development and customer acquisition strategies, intellectual property development and licensing, confidentiality and proprietary information protection, governance structure and decision-making processes, performance metrics and success measurements, revenue sharing and cost allocation, exclusivity provisions and competitive restrictions, termination procedures and relationship wind-down, and dispute resolution mechanisms. Address regulatory approvals and compliance requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Strategic Alliance Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Outsourcing Agreement', 'Business process outsourcing contract with service levels and data security', 'Business Contracts', 'Create a comprehensive outsourcing agreement for business process services. Include detailed service descriptions and performance standards, service level agreements with penalties and remedies, data security and privacy protection requirements, business continuity and disaster recovery procedures, staff qualifications and training requirements, pricing mechanisms and cost management, intellectual property ownership and licensing, regulatory compliance and audit rights, change management and service evolution procedures, termination procedures with knowledge transfer requirements, and dispute resolution mechanisms. Address offshore considerations and regulatory compliance.', 'Premium'
where not exists (select 1 from public.templates where title = 'Outsourcing Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Licensing Agreement (Business)', 'Commercial licensing contract for business methods and processes', 'Business Contracts', 'Draft a comprehensive business licensing agreement for proprietary methods or processes. Include licensed technology or process descriptions, territorial and field-of-use restrictions, exclusivity provisions and competitive limitations, royalty structure and payment terms, minimum performance requirements and sales targets, quality control and brand protection standards, intellectual property protection and enforcement, sublicensing rights and restrictions, reporting and audit requirements, improvement and derivative work ownership, termination procedures and post-termination obligations, and dispute resolution mechanisms. Ensure compliance with antitrust and competition laws.', 'Premium'
where not exists (select 1 from public.templates where title = 'Licensing Agreement (Business)');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Merger Agreement', 'Corporate merger transaction with due diligence and closing conditions', 'Business Contracts', 'Create a comprehensive merger agreement for corporate transactions. Include transaction structure and consideration details, representations and warranties of all parties, due diligence requirements and disclosure schedules, closing conditions and regulatory approvals, employee and benefit plan treatment, intellectual property and contract assignments, indemnification provisions and escrow arrangements, termination rights and break-up fees, post-closing integration procedures, regulatory compliance and antitrust considerations, and dispute resolution mechanisms. Address tax treatment and accounting methods for the transaction.', 'Premium'
where not exists (select 1 from public.templates where title = 'Merger Agreement');

-- Employment Contracts (10)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Employment Agreement (Executive)', 'Executive employment contract with compensation and benefits package', 'Employment Contracts', 'Draft a comprehensive executive employment agreement with detailed position description and reporting structure, base salary and variable compensation arrangements, equity participation and stock option provisions, comprehensive benefits package including health, retirement, and perquisites, vacation and leave policies, expense reimbursement procedures, confidentiality and proprietary information protection, non-compete and non-solicitation provisions where legally enforceable, severance and change-in-control benefits, performance evaluation and termination procedures, dispute resolution mechanisms, and regulatory compliance requirements. Ensure compliance with executive compensation regulations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Employment Agreement (Executive)');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Independent Contractor Agreement', 'Contractor services agreement with clear classification and deliverables', 'Employment Contracts', 'Create a comprehensive independent contractor agreement establishing proper worker classification. Include detailed scope of work and project deliverables, payment terms and expense policies, intellectual property ownership and work-for-hire provisions, confidentiality and non-disclosure obligations, independent contractor status confirmation with tax responsibilities, equipment and resource provisions, performance standards and quality requirements, termination procedures and notice requirements, liability limitations and indemnification, and dispute resolution mechanisms. Address regulatory compliance and worker classification requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Independent Contractor Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Non-Compete Agreement', 'Post-employment competition restriction with reasonable scope and duration', 'Employment Contracts', 'Draft a legally enforceable non-compete agreement with reasonable geographic and temporal restrictions. Include specific competitive activities and business restrictions, customer and client non-solicitation provisions, employee non-solicitation and non-recruitment clauses, confidential information and trade secret protection, consideration and compensation arrangements, enforcement procedures and remedies including injunctive relief, severability provisions for jurisdictional compliance, and dispute resolution mechanisms. Ensure compliance with state-specific non-compete laws and recent legislative changes.', 'Premium'
where not exists (select 1 from public.templates where title = 'Non-Compete Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Severance Agreement', 'Employee separation agreement with release and transition terms', 'Employment Contracts', 'Create a comprehensive severance agreement for employee termination. Include severance payment calculation and schedule, continuation of benefits and COBRA provisions, outplacement and career transition services, comprehensive release of claims with age discrimination compliance, confidentiality and non-disparagement obligations, cooperation with investigations and legal proceedings, return of company property and information, non-compete and non-solicitation enforcement, dispute resolution procedures, and regulatory compliance requirements. Address tax implications and withholding obligations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Severance Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Employee Handbook Acknowledgment', 'Employee acknowledgment of policies and procedures receipt', 'Employment Contracts', 'Draft an employee handbook acknowledgment form confirming receipt and understanding of company policies. Include acknowledgment of at-will employment status, anti-discrimination and harassment policies, code of conduct and ethical standards, confidentiality and proprietary information obligations, technology use and social media policies, safety and security procedures, reporting mechanisms for violations, policy update procedures, and dispute resolution processes. Include signature requirements and record-keeping provisions.', 'Premium'
where not exists (select 1 from public.templates where title = 'Employee Handbook Acknowledgment');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Stock Option Agreement', 'Employee equity compensation with vesting and exercise terms', 'Employment Contracts', 'Create a comprehensive stock option agreement for employee equity compensation. Include option grant details and exercise price, vesting schedule with acceleration provisions, exercise procedures and payment methods, tax implications and withholding obligations, restrictions on transfer and assignment, company repurchase rights and tag-along provisions, termination of employment effects on options, change-in-control and liquidity event provisions, regulatory compliance with securities laws, dispute resolution mechanisms, and governing law provisions. Ensure compliance with Section 409A and other tax regulations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Stock Option Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Performance Improvement Plan', 'Employee performance management with measurable goals and timelines', 'Employment Contracts', 'Draft a performance improvement plan establishing clear expectations and measurable goals. Include specific performance deficiencies and improvement requirements, measurable objectives with deadlines and milestones, support and resources to be provided, monitoring and evaluation procedures, consequences for meeting or failing to meet objectives, employee acknowledgment and commitment requirements, documentation and record-keeping procedures, and legal compliance considerations. Ensure fair and consistent application of performance standards.', 'Premium'
where not exists (select 1 from public.templates where title = 'Performance Improvement Plan');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Restrictive Covenant Agreement', 'Comprehensive post-employment restrictions and confidentiality', 'Employment Contracts', 'Create a comprehensive restrictive covenant agreement combining multiple post-employment obligations. Include confidentiality and trade secret protection, customer and client non-solicitation provisions, employee non-recruitment restrictions, non-compete limitations with reasonable scope, intellectual property assignment and protection, return of company property requirements, cooperation with legal proceedings, dispute resolution mechanisms including injunctive relief, severability provisions for jurisdictional compliance, and consideration arrangements. Ensure enforceability across multiple jurisdictions.', 'Premium'
where not exists (select 1 from public.templates where title = 'Restrictive Covenant Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Consulting Agreement (Former Employee)', 'Post-employment consulting services with conflict management', 'Employment Contracts', 'Draft a consulting agreement for former employee services with conflict of interest management. Include specific consulting services and deliverables, compensation and expense reimbursement, confidentiality obligations for both parties, conflict of interest identification and management, intellectual property ownership and licensing, liability limitations and indemnification, term and termination procedures, compliance with existing restrictive covenants, regulatory compliance requirements, and dispute resolution mechanisms. Address potential conflicts with new employment or business ventures.', 'Premium'
where not exists (select 1 from public.templates where title = 'Consulting Agreement (Former Employee)');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Internship Agreement', 'Student internship program with educational objectives and legal compliance', 'Employment Contracts', 'Create a comprehensive internship agreement complying with labor law requirements. Include educational objectives and learning outcomes, supervision and mentorship arrangements, work schedule and time commitment, compensation or academic credit arrangements, confidentiality and proprietary information protection, intellectual property ownership and assignment, workplace safety and harassment policies, performance evaluation procedures, termination and completion requirements, regulatory compliance with intern labor laws, and dispute resolution mechanisms. Ensure compliance with Department of Labor internship criteria.', 'Premium'
where not exists (select 1 from public.templates where title = 'Internship Agreement');

-- Sales Contracts (10)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Purchase Order Agreement', 'Comprehensive purchase order terms for goods and services procurement', 'Sales Contracts', 'Draft a comprehensive purchase order agreement establishing terms for goods and services procurement. Include detailed product or service specifications, quantity and delivery requirements, pricing and payment terms with early payment discounts, quality standards and acceptance procedures, warranty provisions and defect remedies, delivery schedules and logistics coordination, force majeure and supply disruption procedures, intellectual property rights and proprietary information protection, regulatory compliance and certification requirements, termination and cancellation procedures, and dispute resolution mechanisms. Address international trade and customs requirements where applicable.', 'Premium'
where not exists (select 1 from public.templates where title = 'Purchase Order Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Sales Representative Agreement', 'Commission-based sales representation with territory and performance terms', 'Sales Contracts', 'Create a comprehensive sales representative agreement for commission-based sales. Include territorial boundaries and customer assignments, product lines and sales authority, commission structure and payment terms, sales targets and performance requirements, marketing and promotional support, customer relationship management, intellectual property licensing and brand usage, confidentiality and proprietary information protection, termination procedures and commission reconciliation, non-compete and non-solicitation provisions where enforceable, and dispute resolution mechanisms. Address regulatory compliance and tax obligations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Sales Representative Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Exclusive Dealing Agreement', 'Exclusive supplier or distributor arrangement with performance commitments', 'Sales Contracts', 'Draft an exclusive dealing agreement establishing sole supplier or distributor relationships. Include exclusivity scope and territorial boundaries, minimum purchase or sales commitments, pricing mechanisms and volume discounts, performance standards and measurement criteria, marketing and promotional obligations, intellectual property licensing and protection, confidentiality and proprietary information safeguards, termination procedures and transition requirements, regulatory compliance and antitrust considerations, and dispute resolution mechanisms. Ensure compliance with competition laws and exclusive dealing regulations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Exclusive Dealing Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Product Warranty Agreement', 'Comprehensive product warranty with coverage terms and claim procedures', 'Sales Contracts', 'Create a detailed product warranty agreement defining coverage and claim procedures. Include warranty scope and duration with specific coverage terms, exclusions and limitations on warranty coverage, claim procedures and documentation requirements, repair, replacement, or refund remedies, customer obligations and maintenance requirements, limitation of liability and consequential damages, warranty transfer and assignment provisions, dispute resolution and arbitration procedures, regulatory compliance with warranty laws, and international warranty considerations. Address extended warranty and service plan options.', 'Premium'
where not exists (select 1 from public.templates where title = 'Product Warranty Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Supply Chain Agreement', 'Multi-tier supplier relationship with quality and delivery standards', 'Sales Contracts', 'Draft a comprehensive supply chain agreement for multi-tier supplier relationships. Include supplier qualification and certification requirements, quality standards and continuous improvement programs, delivery schedules and just-in-time requirements, pricing mechanisms and cost reduction targets, inventory management and forecasting procedures, business continuity and risk management, intellectual property protection and confidentiality, regulatory compliance and audit rights, sustainability and social responsibility standards, termination procedures and supplier transition, and dispute resolution mechanisms. Address supply chain transparency and traceability requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Supply Chain Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Consignment Agreement', 'Consignment sales arrangement with inventory management and payment terms', 'Sales Contracts', 'Create a comprehensive consignment agreement for inventory placement and sales. Include consignment inventory management and tracking, pricing and markup arrangements, payment terms and settlement procedures, insurance and risk of loss provisions, marketing and promotional responsibilities, inventory reporting and reconciliation, return and disposal of unsold inventory, intellectual property licensing and brand protection, termination procedures and inventory settlement, regulatory compliance and tax considerations, and dispute resolution mechanisms. Address title retention and creditor protection issues.', 'Premium'
where not exists (select 1 from public.templates where title = 'Consignment Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Volume Purchase Agreement', 'Long-term volume purchasing contract with pricing tiers and commitments', 'Sales Contracts', 'Draft a volume purchase agreement for long-term procurement relationships. Include volume commitment tiers and pricing schedules, delivery schedules and inventory management, quality standards and acceptance procedures, payment terms and credit arrangements, force majeure and supply disruption procedures, price adjustment mechanisms and market fluctuations, intellectual property rights and proprietary specifications, regulatory compliance and certification requirements, performance monitoring and relationship management, termination procedures and volume reconciliation, and dispute resolution mechanisms. Address international trade and currency considerations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Volume Purchase Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Auction Terms and Conditions', 'Auction sale terms with bidding procedures and payment requirements', 'Sales Contracts', 'Create comprehensive auction terms and conditions for sale events. Include bidding procedures and registration requirements, reserve prices and minimum bid increments, payment terms and acceptable payment methods, inspection periods and condition disclosures, title transfer and delivery procedures, buyer''s premium and additional charges, warranty disclaimers and as-is sale provisions, dispute resolution and bid protest procedures, regulatory compliance and licensing requirements, and post-sale obligations and remedies. Address online auction considerations and technology requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Auction Terms and Conditions');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Installment Sale Agreement', 'Installment payment contract with security interest and default remedies', 'Sales Contracts', 'Draft an installment sale agreement with secured payment terms. Include purchase price and down payment requirements, installment payment schedule and interest calculations, security interest and collateral descriptions, insurance requirements and risk of loss, default definitions and cure procedures, acceleration and repossession rights, deficiency and surplus procedures, warranty provisions and maintenance obligations, assignment and transfer restrictions, regulatory compliance with consumer protection laws, and dispute resolution mechanisms. Ensure compliance with Truth in Lending and other consumer finance regulations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Installment Sale Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'International Sales Contract', 'Cross-border sales agreement with Incoterms and trade compliance', 'Sales Contracts', 'Create a comprehensive international sales contract for cross-border transactions. Include Incoterms and delivery terms with risk allocation, currency and payment arrangements with exchange rate protection, export/import licensing and regulatory compliance, customs and duty responsibilities, quality standards and inspection procedures, force majeure and trade disruption provisions, intellectual property protection and enforcement, dispute resolution with international arbitration, governing law and jurisdiction selection, and cultural and language considerations. Address sanctions compliance and trade finance requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'International Sales Contract');

-- Intellectual Property Contracts (10)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Patent License Agreement', 'Patent licensing contract with royalty terms and field restrictions', 'Intellectual Property', 'Draft a comprehensive patent license agreement with detailed licensing terms. Include patent descriptions and claim coverage, field of use and territorial restrictions, exclusivity provisions and competitive limitations, royalty structure and payment terms, minimum royalty guarantees and sales reporting, sublicensing rights and restrictions, patent prosecution and maintenance responsibilities, infringement enforcement and defense obligations, improvement and derivative patent ownership, termination procedures and post-termination obligations, dispute resolution and patent validity challenges, and regulatory compliance requirements. Address patent portfolio licensing and cross-licensing arrangements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Patent License Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Trademark License Agreement', 'Trademark licensing with brand standards and quality control', 'Intellectual Property', 'Create a comprehensive trademark license agreement with brand protection provisions. Include trademark descriptions and registration details, licensed goods and services with quality standards, territorial and temporal restrictions, exclusivity provisions and competitive limitations, royalty structure and minimum guarantees, quality control and brand compliance requirements, marketing and advertising guidelines, trademark prosecution and enforcement obligations, sublicensing rights and restrictions, termination procedures and brand transition, dispute resolution and trademark validity, and regulatory compliance requirements. Address co-branding and trademark coexistence arrangements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Trademark License Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Copyright Assignment Agreement', 'Copyright transfer with moral rights and attribution provisions', 'Intellectual Property', 'Draft a comprehensive copyright assignment agreement for creative works transfer. Include detailed work descriptions and copyright scope, assignment of all rights, title, and interest, moral rights and attribution requirements, consideration and payment terms, warranty and indemnification provisions, reversion rights and termination conditions, derivative work and adaptation rights, international copyright protection, dispute resolution mechanisms, and regulatory compliance requirements. Address work-for-hire determinations and joint authorship issues.', 'Premium'
where not exists (select 1 from public.templates where title = 'Copyright Assignment Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Software License Agreement', 'Software licensing with usage restrictions and support terms', 'Intellectual Property', 'Create a comprehensive software license agreement with detailed usage terms. Include software descriptions and functionality, license scope and usage restrictions, installation and deployment limitations, maintenance and support obligations, updates and version control procedures, intellectual property protection and reverse engineering restrictions, data security and privacy requirements, liability limitations and warranty disclaimers, termination procedures and data migration, regulatory compliance and export controls, and dispute resolution mechanisms. Address cloud deployment and SaaS considerations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Software License Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Trade Secret License Agreement', 'Confidential information licensing with protection and non-disclosure', 'Intellectual Property', 'Draft a trade secret license agreement with comprehensive confidentiality protections. Include trade secret descriptions and confidentiality classifications, permitted uses and disclosure limitations, security measures and access controls, sublicensing restrictions and third-party protections, consideration and royalty arrangements, breach remedies and injunctive relief, return and destruction of confidential information, employee and contractor obligations, termination procedures and post-termination confidentiality, dispute resolution and enforcement mechanisms, and regulatory compliance requirements. Address international trade secret protection and enforcement.', 'Premium'
where not exists (select 1 from public.templates where title = 'Trade Secret License Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'IP Assignment Agreement (Employee)', 'Employee intellectual property assignment with invention disclosure', 'Intellectual Property', 'Create a comprehensive employee IP assignment agreement with invention disclosure requirements. Include assignment of all work-related intellectual property, invention disclosure and reporting obligations, prior invention exclusions and retained rights, consideration and compensation arrangements, cooperation with patent prosecution, moral rights and attribution waivers where applicable, post-employment obligations and restrictions, dispute resolution mechanisms, regulatory compliance with state invention laws, and international assignment considerations. Address shop rights and employer invention policies.', 'Premium'
where not exists (select 1 from public.templates where title = 'IP Assignment Agreement (Employee)');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Content License Agreement', 'Digital content licensing with distribution and usage rights', 'Intellectual Property', 'Draft a comprehensive content license agreement for digital media and creative works. Include content descriptions and media formats, usage rights and distribution channels, territorial and temporal restrictions, exclusivity provisions and competitive limitations, royalty structure and revenue sharing, attribution and credit requirements, quality standards and technical specifications, modification and derivative work rights, termination procedures and content removal, intellectual property warranties and indemnification, dispute resolution mechanisms, and regulatory compliance requirements. Address platform-specific licensing and content moderation.', 'Premium'
where not exists (select 1 from public.templates where title = 'Content License Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Technology Transfer Agreement', 'Technology licensing with know-how transfer and training', 'Intellectual Property', 'Create a comprehensive technology transfer agreement with knowledge transfer provisions. Include technology descriptions and technical specifications, know-how transfer and training obligations, intellectual property licensing and assignment, field of use and territorial restrictions, royalty structure and milestone payments, technical support and maintenance services, improvement and derivative technology ownership, confidentiality and proprietary information protection, regulatory compliance and export controls, termination procedures and technology return, dispute resolution mechanisms, and international technology transfer considerations. Address government funding and march-in rights.', 'Premium'
where not exists (select 1 from public.templates where title = 'Technology Transfer Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Coexistence Agreement', 'Trademark coexistence with usage guidelines and conflict resolution', 'Intellectual Property', 'Draft a trademark coexistence agreement for similar marks in related fields. Include trademark descriptions and registration details, coexistence terms and usage guidelines, territorial and market segment divisions, quality control and brand standards, cross-licensing and reciprocal rights, enforcement cooperation and dispute resolution, modification and expansion procedures, termination conditions and mark transition, regulatory compliance and registration coordination, and international coexistence considerations. Address consumer confusion prevention and market monitoring obligations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Coexistence Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'IP Litigation Settlement Agreement', 'Intellectual property dispute settlement with licensing and covenants', 'Intellectual Property', 'Create a comprehensive IP litigation settlement agreement resolving patent, trademark, or copyright disputes. Include dispute resolution and claim releases, cross-licensing and covenant not to sue provisions, monetary settlements and payment terms, ongoing royalty arrangements if applicable, confidentiality and non-disclosure obligations, compliance monitoring and enforcement, modification and amendment procedures, breach remedies and dispute resolution, regulatory compliance and court approval requirements, and international enforcement considerations. Address future IP development and competitive activities.', 'Premium'
where not exists (select 1 from public.templates where title = 'IP Litigation Settlement Agreement');

-- Financial Contracts (8)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Loan Agreement', 'Commercial loan contract with security and repayment terms', 'Financial Contracts', 'Draft a comprehensive commercial loan agreement with detailed lending terms. Include loan amount and disbursement procedures, interest rate and payment calculations, repayment schedule and prepayment options, security interest and collateral descriptions, financial covenants and reporting requirements, default definitions and cure procedures, acceleration and enforcement rights, guaranty and personal liability provisions, regulatory compliance with banking laws, insurance and risk management requirements, dispute resolution mechanisms, and international lending considerations. Address regulatory capital and compliance requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Loan Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Investment Agreement', 'Equity investment contract with investor rights and protections', 'Financial Contracts', 'Create a comprehensive investment agreement for equity financing transactions. Include investment amount and valuation terms, securities descriptions and rights attached, investor protection provisions and information rights, board representation and governance rights, anti-dilution and preemptive rights, drag-along and tag-along provisions, liquidation preferences and distribution rights, registration rights and transfer restrictions, representations and warranties of all parties, closing conditions and regulatory approvals, dispute resolution mechanisms, and regulatory compliance with securities laws. Address venture capital and private equity considerations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Investment Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Promissory Note', 'Debt instrument with payment terms and default provisions', 'Financial Contracts', 'Draft a comprehensive promissory note establishing debt obligations and repayment terms. Include principal amount and interest calculations, payment schedule and maturity date, prepayment options and penalties, default definitions and acceleration rights, security interest and collateral provisions, guaranty and endorsement requirements, collection costs and attorney fees, regulatory compliance with usury laws, dispute resolution and governing law, and negotiability and transfer restrictions. Address convertible note features and equity conversion terms where applicable.', 'Premium'
where not exists (select 1 from public.templates where title = 'Promissory Note');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Security Agreement', 'Secured transaction with collateral description and enforcement rights', 'Financial Contracts', 'Create a comprehensive security agreement establishing security interests in personal property. Include detailed collateral descriptions and classifications, security interest perfection and filing requirements, debtor representations and warranties, insurance and maintenance obligations, default definitions and enforcement procedures, collection and disposition rights, surplus and deficiency procedures, additional advances and future obligations, release and termination conditions, regulatory compliance with UCC requirements, dispute resolution mechanisms, and international secured transaction considerations. Address purchase money security interests and priority issues.', 'Premium'
where not exists (select 1 from public.templates where title = 'Security Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Guaranty Agreement', 'Personal or corporate guaranty with liability limitations and enforcement terms', 'Financial Contracts', 'Draft a comprehensive guaranty agreement with detailed liability and enforcement terms. Include guaranteed obligations and liability scope, continuing guaranty and future advances, personal or corporate guaranty distinctions, liability limitations and caps where applicable, default definitions and enforcement procedures, waiver of defenses and suretyship rights, subordination and standstill provisions, release and discharge conditions, regulatory compliance and consumer protection laws, dispute resolution mechanisms, and international guaranty enforcement. Address joint and several liability and contribution rights.', 'Premium'
where not exists (select 1 from public.templates where title = 'Guaranty Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Factoring Agreement', 'Accounts receivable financing with recourse and collection terms', 'Financial Contracts', 'Create a comprehensive factoring agreement for accounts receivable financing. Include receivables eligibility criteria and concentration limits, advance rates and reserve requirements, recourse and non-recourse provisions, collection procedures and customer notification, fees and interest calculations, representations and warranties regarding receivables, default definitions and remedies, termination procedures and account reconciliation, regulatory compliance with commercial finance laws, dispute resolution mechanisms, and international factoring considerations. Address invoice verification and dilution reserves.', 'Premium'
where not exists (select 1 from public.templates where title = 'Factoring Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Equipment Financing Agreement', 'Equipment purchase financing with title retention and default remedies', 'Financial Contracts', 'Draft a comprehensive equipment financing agreement with security and repayment provisions. Include equipment descriptions and purchase terms, financing amount and payment schedule, title retention and security interest provisions, insurance and maintenance requirements, use restrictions and location requirements, default definitions and cure procedures, repossession and disposition rights, deficiency and surplus procedures, regulatory compliance with consumer and commercial finance laws, dispute resolution mechanisms, and international equipment financing considerations. Address lease-purchase options and residual value guarantees.', 'Premium'
where not exists (select 1 from public.templates where title = 'Equipment Financing Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Credit Agreement', 'Revolving credit facility with borrowing base and financial covenants', 'Financial Contracts', 'Create a comprehensive credit agreement establishing revolving credit facilities. Include credit limit and borrowing base calculations, interest rate and fee structures, borrowing and repayment procedures, financial covenants and reporting requirements, material adverse change and default provisions, security interest and collateral requirements, guaranty and cross-default provisions, regulatory compliance with banking regulations, amendment and waiver procedures, dispute resolution mechanisms, and international credit facility considerations. Address syndicated lending and agent bank relationships.', 'Premium'
where not exists (select 1 from public.templates where title = 'Credit Agreement');

-- International Contracts (8)
insert into public.templates (title, description, category, prompt, required_plan)
select 'International Distribution Agreement', 'Cross-border distribution with regulatory compliance and currency terms', 'International Contracts', 'Draft a comprehensive international distribution agreement for cross-border product distribution. Include territorial boundaries and exclusivity provisions, regulatory compliance and import/export requirements, currency and payment arrangements with exchange rate protection, pricing and transfer pricing considerations, intellectual property protection and enforcement, cultural and language adaptations, dispute resolution with international arbitration, governing law and jurisdiction selection, force majeure and political risk provisions, termination procedures and inventory management, and regulatory compliance with international trade laws. Address sanctions compliance and anti-corruption requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'International Distribution Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Cross-Border Service Agreement', 'International service contract with tax and regulatory compliance', 'International Contracts', 'Create a comprehensive cross-border service agreement with international compliance provisions. Include service descriptions and performance standards, cross-border tax implications and withholding requirements, regulatory compliance and professional licensing, currency and payment arrangements, data protection and privacy compliance with GDPR and local laws, intellectual property protection and enforcement, cultural and language considerations, dispute resolution with international arbitration, governing law and jurisdiction selection, force majeure and political risk provisions, termination procedures and knowledge transfer, and regulatory compliance with international service regulations. Address visa and work permit requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Cross-Border Service Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Import/Export Agreement', 'International trade contract with Incoterms and customs compliance', 'International Contracts', 'Draft a comprehensive import/export agreement for international trade transactions. Include Incoterms and delivery terms with risk allocation, customs and duty responsibilities, export/import licensing and regulatory compliance, currency and payment arrangements with trade finance, quality standards and inspection procedures, force majeure and trade disruption provisions, intellectual property protection and customs enforcement, dispute resolution with international arbitration, governing law and jurisdiction selection, sanctions compliance and restricted party screening, and regulatory compliance with international trade laws. Address trade finance and documentary credit requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Import/Export Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'International Joint Venture Agreement', 'Cross-border joint venture with regulatory approvals and governance', 'International Contracts', 'Create a comprehensive international joint venture agreement for cross-border business collaboration. Include venture structure and regulatory approvals, partner contributions and resource allocation, governance structure and management committee, profit and loss sharing with transfer pricing, intellectual property development and cross-licensing, regulatory compliance and foreign investment approvals, tax planning and treaty benefits, dispute resolution with international arbitration, governing law and multi-jurisdictional enforcement, cultural and language considerations, termination procedures and asset distribution, and regulatory compliance with international investment laws. Address currency controls and repatriation restrictions.', 'Premium'
where not exists (select 1 from public.templates where title = 'International Joint Venture Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Foreign Investment Agreement', 'International investment contract with regulatory approvals and protections', 'International Contracts', 'Draft a comprehensive foreign investment agreement with regulatory compliance and investor protections. Include investment structure and regulatory approvals, investor rights and protections under bilateral investment treaties, local partner requirements and joint venture structures, regulatory compliance and foreign investment restrictions, tax planning and treaty benefits, currency controls and repatriation procedures, dispute resolution with international arbitration and investor-state mechanisms, governing law and enforcement considerations, political risk and expropriation protection, termination procedures and exit strategies, and regulatory compliance with foreign investment laws. Address sovereign immunity and diplomatic protection issues.', 'Premium'
where not exists (select 1 from public.templates where title = 'Foreign Investment Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'International Licensing Agreement', 'Cross-border IP licensing with territorial restrictions and compliance', 'International Contracts', 'Create a comprehensive international licensing agreement for intellectual property rights. Include IP descriptions and territorial licensing scope, regulatory compliance and IP registration requirements, currency and payment arrangements with transfer pricing, sublicensing rights and territorial restrictions, quality control and brand protection standards, regulatory compliance and export control restrictions, dispute resolution with international arbitration, governing law and multi-jurisdictional enforcement, cultural and language adaptations, termination procedures and IP transition, and regulatory compliance with international IP laws. Address parallel imports and exhaustion of rights issues.', 'Premium'
where not exists (select 1 from public.templates where title = 'International Licensing Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Cross-Border Data Transfer Agreement', 'International data processing with privacy law compliance', 'International Contracts', 'Draft a comprehensive cross-border data transfer agreement complying with international privacy laws. Include data transfer mechanisms and adequacy decisions, standard contractual clauses and binding corporate rules, data protection impact assessments and transfer risk analysis, data subject rights and cross-border enforcement, security measures and breach notification procedures, regulatory compliance with GDPR, CCPA, and local privacy laws, dispute resolution and supervisory authority cooperation, governing law and jurisdictional considerations, data localization and residency requirements, termination procedures and data return/deletion, and regulatory compliance with international data protection laws. Address government access and surveillance considerations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Cross-Border Data Transfer Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'International Franchise Agreement', 'Cross-border franchising with local law compliance and cultural adaptation', 'International Contracts', 'Create a comprehensive international franchise agreement with local market adaptations. Include franchise territory and master franchise arrangements, local law compliance and franchise registration requirements, cultural adaptations and local market considerations, currency and payment arrangements with transfer pricing, intellectual property protection and local trademark registration, training and support with cultural sensitivity, quality control and brand consistency standards, regulatory compliance and disclosure requirements, dispute resolution with international arbitration, governing law and multi-jurisdictional enforcement, termination procedures and local asset protection, and regulatory compliance with international franchise laws. Address local partner requirements and joint venture structures.', 'Premium'
where not exists (select 1 from public.templates where title = 'International Franchise Agreement');

-- Specialized Contracts (8)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Entertainment Contract', 'Artist or performer agreement with royalties and creative control', 'Specialized Contracts', 'Draft a comprehensive entertainment contract for artists, performers, or creative professionals. Include performance or creative services descriptions, compensation structure with royalties and residuals, creative control and artistic approval rights, intellectual property ownership and licensing, publicity and marketing obligations, exclusivity and non-compete provisions, union compliance and guild requirements, insurance and liability provisions, force majeure and performance cancellation, dispute resolution with industry arbitration, regulatory compliance with entertainment industry laws, and international distribution and licensing considerations. Address moral rights and attribution requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Entertainment Contract');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Sports Contract', 'Professional athlete agreement with performance incentives and image rights', 'Specialized Contracts', 'Create a comprehensive sports contract for professional athletes with performance-based compensation. Include base salary and performance incentives, image rights and endorsement provisions, injury and disability protections, training and conditioning requirements, team obligations and conduct standards, trade and transfer provisions, agent representation and commission arrangements, insurance and medical coverage, dispute resolution with sports arbitration, regulatory compliance with league and federation rules, international competition and transfer regulations, and post-career transition support. Address drug testing and anti-doping compliance.', 'Premium'
where not exists (select 1 from public.templates where title = 'Sports Contract');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Educational Services Agreement', 'Academic institution service contract with accreditation and compliance', 'Specialized Contracts', 'Draft a comprehensive educational services agreement for academic institutions or educational service providers. Include educational program descriptions and learning outcomes, accreditation and regulatory compliance requirements, student admission and enrollment procedures, tuition and fee structures with refund policies, faculty qualifications and employment terms, intellectual property ownership and licensing, student privacy and FERPA compliance, disability accommodations and accessibility, dispute resolution and grievance procedures, regulatory compliance with education laws, international student and visa considerations, and quality assurance and assessment procedures. Address online education and technology requirements.', 'Premium'
where not exists (select 1 from public.templates where title = 'Educational Services Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Construction Contract', 'Building construction agreement with specifications and payment schedules', 'Specialized Contracts', 'Create a comprehensive construction contract with detailed specifications and project management provisions. Include project scope and construction specifications, payment schedule with progress payments and retainage, change order procedures and cost adjustments, construction timeline and milestone requirements, quality standards and inspection procedures, safety requirements and insurance obligations, lien waiver and payment bond provisions, warranty and defect remediation procedures, force majeure and weather delay provisions, dispute resolution with construction arbitration, regulatory compliance with building codes and permits, and environmental and sustainability requirements. Address subcontractor management and coordination.', 'Premium'
where not exists (select 1 from public.templates where title = 'Construction Contract');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Insurance Contract', 'Insurance policy terms with coverage limits and claim procedures', 'Specialized Contracts', 'Draft a comprehensive insurance contract with detailed coverage terms and claim procedures. Include coverage descriptions and policy limits, premium calculations and payment terms, deductibles and co-insurance provisions, exclusions and limitations on coverage, claim reporting and investigation procedures, settlement and payment obligations, policy renewal and cancellation terms, regulatory compliance with insurance laws, dispute resolution and appraisal procedures, reinsurance and risk transfer arrangements, international coverage and jurisdiction issues, and regulatory compliance with insurance regulations. Address cyber insurance and emerging risk coverage.', 'Premium'
where not exists (select 1 from public.templates where title = 'Insurance Contract');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Government Contract', 'Public sector contracting with compliance and reporting requirements', 'Specialized Contracts', 'Create a comprehensive government contract with public sector compliance requirements. Include contract scope and deliverable specifications, pricing and cost accounting standards, compliance with government procurement regulations, minority and disadvantaged business requirements, security clearance and background check requirements, intellectual property rights and government licensing, audit and inspection rights, regulatory compliance and reporting obligations, dispute resolution with government contract appeals, termination for convenience and default procedures, ethics and conflict of interest requirements, and international government contracting considerations. Address classified information and export control compliance.', 'Premium'
where not exists (select 1 from public.templates where title = 'Government Contract');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Non-Profit Services Agreement', 'Charitable organization service contract with mission alignment and compliance', 'Specialized Contracts', 'Draft a comprehensive non-profit services agreement with charitable purpose and tax-exempt compliance. Include mission-aligned service descriptions and social impact metrics, funding and grant compliance requirements, volunteer management and coordination, donor privacy and stewardship obligations, regulatory compliance with charity and tax-exempt laws, board governance and fiduciary duties, conflict of interest and ethics policies, financial reporting and transparency requirements, dispute resolution and mediation procedures, international charitable activities and compliance, fundraising and solicitation regulations, and program evaluation and outcome measurement. Address political activity restrictions and lobbying limitations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Non-Profit Services Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Environmental Services Agreement', 'Environmental consulting and remediation contract with regulatory compliance', 'Specialized Contracts', 'Create a comprehensive environmental services agreement for consulting, testing, or remediation services. Include environmental scope and technical specifications, regulatory compliance with environmental laws, sampling and testing procedures with chain of custody, remediation standards and cleanup criteria, health and safety requirements and protocols, insurance and liability provisions for environmental risks, regulatory reporting and agency coordination, community engagement and public participation, dispute resolution and expert determination, long-term monitoring and maintenance obligations, regulatory compliance with federal and state environmental laws, and international environmental standards and protocols. Address climate change and sustainability considerations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Environmental Services Agreement');

-- Pricing Plans
insert into public.pricing_plans (name, price, price_detail, features, cta, is_featured, sort_order)
select 'Registered User', 'Free', '/ month', ARRAY[
  '5 document generations/month',
  'Standard templates',
  'Document history & versions',
  'E-signatures',
  'Comments & notifications',
  'Email support'
]::text[], 'Get Started', false, 1
where not exists (select 1 from public.pricing_plans where name = 'Registered User');

insert into public.pricing_plans (name, price, price_detail, features, cta, is_featured, sort_order)
select 'Premium', '$49', '/ month', ARRAY[
  'Unlimited generations',
  'AI document analysis & suggestions',
  'Clause library & custom templates',
  'Contract lifecycle & approvals',
  'Obligations tracking & key dates',
  'Workflow automation',
  'Team collaboration',
  'Integrations',
  'Priority support'
]::text[], 'Upgrade', true, 2
where not exists (select 1 from public.pricing_plans where name = 'Premium');

insert into public.pricing_plans (name, price, price_detail, features, cta, is_featured, sort_order)
select 'Enterprise', 'Custom', '', ARRAY[
  'Everything in Premium',
  'Advanced security & SSO',
  'API & custom integrations',
  'Audit logs & controls',
  'Dedicated account manager'
]::text[], 'Contact Sales', false, 3
where not exists (select 1 from public.pricing_plans where name = 'Enterprise');
