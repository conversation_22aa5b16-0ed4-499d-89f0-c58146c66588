import { describe, expect, it } from 'vitest';

describe('Template Workflow State Management', () => {
  it('should clear both promptToGenerate and isTemplateSession when clearPromptToGenerate is called', () => {
    // Simulate the state management logic from DashboardLayout
    let promptToGenerate: string | null = null;
    let isTemplateSession = false;

    // Simulate handleSelectTemplate
    const handleSelectTemplate = (prompt: string) => {
      promptToGenerate = prompt;
      isTemplateSession = true;
    };

    // Simulate clearPromptToGenerate (with our fix)
    const clearPromptToGenerate = () => {
      promptToGenerate = null;
      isTemplateSession = false; // This is the fix we implemented
    };

    // Test the workflow
    handleSelectTemplate('Create a professional NDA');
    expect(promptToGenerate).toBe('Create a professional NDA');
    expect(isTemplateSession).toBe(true);

    // Clear the prompt (this should also clear the template session)
    clearPromptToGenerate();
    expect(promptToGenerate).toBe(null);
    expect(isTemplateSession).toBe(false);
  });

  it('should properly tag documents based on fromTemplate flag', () => {
    // Simulate the handleSaveFromModal logic from ChatInterface
    const mockOnSaveDocument = (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => {
      return { content, name, folderId, clientId, options };
    };

    // Test with fromTemplate = true
    const fromTemplate = true;
    const tags: string[] = [];
    if (fromTemplate) {
      tags.push('origin:template');
    }

    const result1 = mockOnSaveDocument('content', 'Test Doc', null, null, { tags });
    expect(result1.options?.tags).toEqual(['origin:template']);

    // Test with fromTemplate = false
    const fromTemplate2 = false;
    const tags2: string[] = [];
    if (fromTemplate2) {
      tags2.push('origin:template');
    }

    const result2 = mockOnSaveDocument('content', 'Test Doc', null, null, { tags: tags2 });
    expect(result2.options?.tags).toEqual([]);
  });
});
