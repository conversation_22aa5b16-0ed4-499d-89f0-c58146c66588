import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { ANONYMOUS_USER_QUOTA, INITIAL_MESSAGE } from '../constants';
import { apiFetch } from '../lib/api';
import { AssistanceType, ChatMessage, ConversationStage, ConversationState, MessageRole, ToneType, User } from '../types';
// Removed streaming imports - no longer needed
import { cn } from '../lib/utils';
import DocumentViewerModal from './DocumentViewerModal';
import { BotIcon, DocumentIcon, MicrophoneIcon, MicrophoneSlashIcon, SendIcon, StopIcon, UserIcon } from './Icons';
import MarkdownRenderer from './MarkdownRenderer';
import SidePanel from './SidePanel';
import Dropdown from './ui/Dropdown';

// Speech Recognition type declaration
interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
}

interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

declare global {
  interface Window {
    SpeechRecognition: {
      new(): SpeechRecognition;
    };
    webkitSpeechRecognition: {
      new(): SpeechRecognition;
    };
  }
}

const SUGGESTED_PROMPTS = [
  "Draft a simple Non-Disclosure Agreement (NDA)",
  "Create a freelance contract for a web developer",
  "Write a basic rental lease agreement clause",
];

interface ChatInterfaceProps {
  setView?: (view: 'home' | 'auth' | 'dashboard') => void;
  user?: User | null;
  onSaveDocument?: (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => void;
  initialPrompt?: string | null;
  onInitialPromptHandled?: () => void;
  isDemo?: boolean;
  fromTemplate?: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = memo(({ setView, user, onSaveDocument, initialPrompt, onInitialPromptHandled, isDemo = false, fromTemplate = false }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([INITIAL_MESSAGE]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [anonymousQuota, setAnonymousQuota] = useState(ANONYMOUS_USER_QUOTA);
  const [reviewModalContent, setReviewModalContent] = useState<string | null>(null);
  const [isSidePanelExpanded, setIsSidePanelExpanded] = useState(() => {
    // Get persistent state from localStorage
    const saved = localStorage.getItem('sidePanelExpanded');
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Enhanced conversation state
  const [conversationState, setConversationState] = useState<ConversationState>({
    currentStage: ConversationStage.INITIAL,
    collectedData: {},
    isComplete: false
  });
  const [isTyping, setIsTyping] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<string | null>(null);

  // New state for enhanced input features
  const [assistanceType, setAssistanceType] = useState<AssistanceType>('Draft Contract');
  const [tone, setTone] = useState<ToneType>('Professional');
  const [isRecording, setIsRecording] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Generation progress tracking
  const [generationProgress, setGenerationProgress] = useState<number>(0);
  const [canCancelGeneration, setCanCancelGeneration] = useState(false);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);

  // Save sidebar state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidePanelExpanded', JSON.stringify(isSidePanelExpanded));
  }, [isSidePanelExpanded]);
  const abortRef = useRef<AbortController | null>(null);
  const loadingTimeoutRef = useRef<number | null>(null);

  // Auto-resize textarea function
  const autoResizeTextarea = useCallback(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, []);

  // Speech-to-text functionality
  const initializeSpeechRecognition = useCallback(() => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition not supported');
      setError('Speech recognition is not supported in this browser. Please try Chrome or Edge.');
      return null;
    }

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.continuous = false; // Changed to false for better control
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onstart = () => {
        console.log('Speech recognition started');
        setIsRecording(true);
        setError(null); // Clear any previous errors
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        setIsRecording(false);
      };

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = 0; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        if (finalTranscript) {
          console.log('Final transcript:', finalTranscript);
          setInput(prev => {
            const newValue = prev + (prev ? ' ' : '') + finalTranscript.trim();
            return newValue;
          });
          setTimeout(autoResizeTextarea, 0);
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsRecording(false);

        let errorMessage = 'Speech recognition error occurred.';
        switch (event.error) {
          case 'no-speech':
            errorMessage = 'No speech was detected. Please try again.';
            break;
          case 'audio-capture':
            errorMessage = 'Audio capture failed. Please check your microphone.';
            break;
          case 'not-allowed':
            errorMessage = 'Microphone access denied. Please allow microphone access.';
            break;
          case 'network':
            errorMessage = 'Network error occurred during speech recognition.';
            break;
          default:
            errorMessage = `Speech recognition error: ${event.error}`;
        }
        setError(errorMessage);
      };

      return recognition;
    } catch (error) {
      console.error('Failed to initialize speech recognition:', error);
      setError('Failed to initialize speech recognition.');
      return null;
    }
  }, [autoResizeTextarea]);

  const startRecording = useCallback(() => {
    try {
      if (!recognition) {
        const newRecognition = initializeSpeechRecognition();
        if (newRecognition) {
          setRecognition(newRecognition);
          console.log('Starting new speech recognition session');
          newRecognition.start();
        } else {
          console.error('Failed to initialize speech recognition');
        }
      } else {
        console.log('Starting existing speech recognition session');
        recognition.start();
      }
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      setError('Failed to start speech recognition. Please try again.');
      setIsRecording(false);
    }
  }, [recognition, initializeSpeechRecognition]);

  const stopRecording = useCallback(() => {
    try {
      if (recognition) {
        console.log('Stopping speech recognition');
        recognition.stop();
      }
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
      setIsRecording(false);
    }
  }, [recognition]);

  // Auto-resize textarea when input changes
  useEffect(() => {
    autoResizeTextarea();
  }, [input, autoResizeTextarea]);

  const extractDocumentFromResponse = (raw: string): { cleaned: string; hadMarkers: boolean; preContractText?: string; hasStageMarker?: boolean } => {
    const start = '---CONTRACT START---';
    const end = '---CONTRACT END---';
    const stageMarker = '---STAGE 1 COMPLETE---';

    // Check for stage marker first
    if (raw.includes(stageMarker)) {
      const stage1Text = raw.substring(0, raw.indexOf(stageMarker)).trim();
      const remainingText = raw.substring(raw.indexOf(stageMarker) + stageMarker.length).trim();

      // Check if remaining text has contract markers
      if (remainingText.includes(start) && remainingText.includes(end)) {
        const contractContent = remainingText.substring(
          remainingText.indexOf(start) + start.length,
          remainingText.indexOf(end)
        ).trim();
        return {
          cleaned: contractContent,
          hadMarkers: true,
          preContractText: stage1Text || undefined,
          hasStageMarker: true
        };
      }

      return {
        cleaned: remainingText || raw.trim(),
        hadMarkers: false,
        preContractText: stage1Text || undefined,
        hasStageMarker: true
      };
    }

    // Original contract marker logic
    if (raw.includes(start) && raw.includes(end)) {
      const preContractText = raw.substring(0, raw.indexOf(start)).trim();
      const between = raw.substring(raw.indexOf(start) + start.length, raw.indexOf(end)).trim();
      return {
        cleaned: between,
        hadMarkers: true,
        preContractText: preContractText || undefined
      };
    }

    // If there's a markdown or prose preface with '---' divider at top, drop up to last divider in first 500 chars
    const first500 = raw.slice(0, 500);
    if ((first500.match(/---/g) || []).length >= 1) {
      const lastDash = first500.lastIndexOf('---');
      if (lastDash >= 0) { raw = raw.slice(lastDash + 3); }
    }
    // Strip common prefaces like "Okay, here's..."
    raw = raw.replace(/^\s*(okay|sure|here's|here is)[^\n]*\n+/i, '');
    // If there's an obvious HTML start, cut everything before it
    const m = raw.match(/<(h1|h2|article|section|p|html)[\s>]/i);
    if (m && typeof m.index === 'number') {
      raw = raw.slice(m.index).trim();
    }
    return { cleaned: raw.trim(), hadMarkers: false };
  };


  const messagesEndRef = useRef<HTMLDivElement>(null);

  const isAnonymous = !user;
  const isPaid = !!user && (user.planName === 'Premium' || user.planName === 'Enterprise');
  // For paid plans, treat as unlimited; anonymous users have session-limited quota
  const remaining = isAnonymous ? anonymousQuota : (isPaid ? Infinity : ((user?.quotaTotal || 0) - (user?.quotaUsed || 0)));
  const atQuotaLimit = isPaid ? false : (remaining <= 0);

  // Chat session handled server-side per request

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  // Handler functions for interactive messages
  const handleSuggestedResponse = useCallback((response: string) => {
    // Add the suggested response as a user message and process it
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: response
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    // Process the response through the conversational flow
    if (conversationState.currentStage === ConversationStage.INITIAL) {
      handleConversationalFlow(response);
    } else {
      handleStandardResponse(response);
    }
  }, [conversationState.currentStage]);

  const handleConfirmation = useCallback((messageId: string) => {
    // Add user confirmation message
    const confirmationMessage: ChatMessage = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: "Yes, please continue with these details."
    };

    setMessages(prev => [...prev, confirmationMessage]);

    // Update conversation state to generating stage
    setConversationState(prev => ({
      ...prev,
      currentStage: ConversationStage.GENERATING_CONTRACT
    }));

    // Send confirmation to AI
    handleStandardResponse("Yes, please continue with these details.");
  }, []);

  const handleModification = useCallback((messageId: string) => {
    // Add user modification request
    const modifyMessage: ChatMessage = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: "I'd like to modify some of the details. Let me provide updated information."
    };

    setMessages(prev => [...prev, modifyMessage]);

    // Reset conversation state to processing request
    setConversationState(prev => ({
      ...prev,
      currentStage: ConversationStage.PROCESSING_REQUEST,
      collectedData: {} // Clear collected data for fresh start
    }));

    // Handle modification request
    handleStandardResponse("I'd like to modify some of the details. Let me provide updated information.");
  }, []);

  // Helper function to get next conversation stage
  const getNextStage = (currentStage: ConversationStage): ConversationStage => {
    const stageOrder = [
      ConversationStage.INITIAL,
      ConversationStage.ANALYZING_REQUEST,
      ConversationStage.PROCESSING_REQUEST,
      ConversationStage.APPLYING_DEFAULTS,
      ConversationStage.PLANNING_STRUCTURE,
      ConversationStage.GENERATING_OUTLINE,
      ConversationStage.GENERATING_CONTRACT,
      ConversationStage.FINALIZING_DOCUMENT,
      ConversationStage.REVIEW_READY,
      ConversationStage.COMPLETED
    ];

    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] : currentStage;
  };

  // Helper function to detect contract requests
  const isContractRequest = (prompt: string): boolean => {
    const contractKeywords = [
      'contract', 'agreement', 'nda', 'non-disclosure', 'lease', 'employment',
      'freelance', 'consulting', 'terms of service', 'privacy policy', 'draft',
      'create', 'generate', 'write'
    ];

    const lowerPrompt = prompt.toLowerCase();
    return contractKeywords.some(keyword => lowerPrompt.includes(keyword));
  };

  // Helper function to get stage progress percentage
  const getStageProgress = (stage: ConversationStage): number => {
    const stageProgressMap = {
      [ConversationStage.INITIAL]: 0,
      [ConversationStage.ANALYZING_REQUEST]: 10,
      [ConversationStage.PROCESSING_REQUEST]: 20,
      [ConversationStage.APPLYING_DEFAULTS]: 35,
      [ConversationStage.PLANNING_STRUCTURE]: 50,
      [ConversationStage.GENERATING_OUTLINE]: 65,
      [ConversationStage.GENERATING_CONTRACT]: 80,
      [ConversationStage.FINALIZING_DOCUMENT]: 95,
      [ConversationStage.REVIEW_READY]: 100,
      [ConversationStage.COMPLETED]: 100
    };

    return stageProgressMap[stage] || 0;
  };

  // Helper function to get stage description
  const getStageDescription = (stage: ConversationStage): string => {
    const stageDescriptions = {
      [ConversationStage.INITIAL]: 'Getting started',
      [ConversationStage.ANALYZING_REQUEST]: 'Understanding your request',
      [ConversationStage.PROCESSING_REQUEST]: 'Processing your requirements',
      [ConversationStage.APPLYING_DEFAULTS]: 'Applying best practices',
      [ConversationStage.PLANNING_STRUCTURE]: 'Planning document structure',
      [ConversationStage.GENERATING_OUTLINE]: 'Creating outline',
      [ConversationStage.GENERATING_CONTRACT]: 'Generating your document',
      [ConversationStage.FINALIZING_DOCUMENT]: 'Finalizing document',
      [ConversationStage.REVIEW_READY]: 'Ready for review',
      [ConversationStage.COMPLETED]: 'Complete'
    };

    return stageDescriptions[stage] || 'In progress';
  };

  // Enhanced immediate generation flow handler
  const handleConversationalFlow = async (prompt: string) => {
    setIsTyping(false);

    // Start immediate generation - no questioning phase
    await handleImmediateGeneration(prompt);
  };

  // New immediate generation handler with progress updates
  const handleImmediateGeneration = async (prompt: string) => {
    const generationId = `generation-${Date.now()}`;

    // Reset progress tracking
    setGenerationProgress(0);
    setCanCancelGeneration(true);
    setEstimatedTimeRemaining(10); // Estimate 10 seconds total

    // Update conversation state to analyzing
    setConversationState(prev => ({
      ...prev,
      currentStage: ConversationStage.ANALYZING_REQUEST
    }));

    // Add initial status message
    const statusMessage: ChatMessage = {
      id: `status-${generationId}`,
      role: MessageRole.MODEL,
      content: "Analyzing your request...",
      messageType: 'status',
      statusType: 'thinking'
    };

    setMessages(prev => [...prev, statusMessage]);

    // Progress through stages with realistic timing and progress updates
    const stages = [
      { stage: ConversationStage.ANALYZING_REQUEST, message: "Analyzing your request...", delay: 1500, progress: 10 },
      { stage: ConversationStage.PROCESSING_REQUEST, message: "Processing requirements...", delay: 1000, progress: 20 },
      { stage: ConversationStage.APPLYING_DEFAULTS, message: "Applying legal best practices...", delay: 1500, progress: 35 },
      { stage: ConversationStage.PLANNING_STRUCTURE, message: "Planning document structure...", delay: 1000, progress: 50 },
      { stage: ConversationStage.GENERATING_OUTLINE, message: "Creating document outline...", delay: 1200, progress: 65 },
      { stage: ConversationStage.GENERATING_CONTRACT, message: "Generating your document...", delay: 2000, progress: 80 },
      { stage: ConversationStage.FINALIZING_DOCUMENT, message: "Finalizing document...", delay: 800, progress: 95 }
    ];

    // Process each stage with progress updates
    for (let i = 0; i < stages.length; i++) {
      const { stage, message, delay, progress } = stages[i];

      // Check if generation was cancelled
      if (abortRef.current?.signal.aborted) {
        setCanCancelGeneration(false);
        setEstimatedTimeRemaining(null);
        return;
      }

      setConversationState(prev => ({ ...prev, currentStage: stage }));
      setGenerationProgress(progress);
      setEstimatedTimeRemaining(Math.max(0, 10 - (i + 1) * 1.4)); // Decrease estimated time

      setMessages(prev => prev.map(msg =>
        msg.id === `status-${generationId}`
          ? { ...msg, content: message, statusType: 'thinking' as const }
          : msg
      ));

      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Remove status message and proceed to document generation
    setMessages(prev => prev.filter(msg => msg.id !== `status-${generationId}`));
    setGenerationProgress(100);
    setCanCancelGeneration(false);
    setEstimatedTimeRemaining(null);

    // Call the immediate generation API
    await handleImmediateGenerationAPI(prompt);
  };

  // Cancel generation function
  const handleCancelGeneration = useCallback(() => {
    if (abortRef.current) {
      abortRef.current.abort();
      setIsLoading(false);
      setCanCancelGeneration(false);
      setGenerationProgress(0);
      setEstimatedTimeRemaining(null);
      setConversationState(prev => ({ ...prev, currentStage: ConversationStage.INITIAL }));

      // Add cancellation message
      const cancelMessage: ChatMessage = {
        id: `cancel-${Date.now()}`,
        role: MessageRole.MODEL,
        content: "Document generation was cancelled. You can start a new request anytime.",
        messageType: 'status'
      };
      setMessages(prev => [...prev, cancelMessage]);
    }
  }, []);

  // New immediate generation API handler
  const handleImmediateGenerationAPI = async (prompt: string) => {
    try {
      // Use the new immediate generation endpoint
      const json = await apiFetch<{
        text: string;
        stage?: string;
        messageType?: string;
      }>(`/api/ai/chat/immediate`, {
        method: 'POST',
        body: JSON.stringify({
          prompt,
          assistanceType,
          tone
        })
      });

      const response = json.text || '';

      // Extract contract content and handle document generation
      const { cleaned, hadMarkers, preContractText } = extractDocumentFromResponse(response);
      let isContract = hadMarkers;
      let contractText = hadMarkers ? cleaned : '';

      // Add the generated document as a message
      const documentMessage: ChatMessage = {
        id: `document-${Date.now()}`,
        role: MessageRole.MODEL,
        content: cleaned || response,
        isContract: hadMarkers,
        messageType: 'document'
      };

      // Add pre-contract text if it exists
      if (preContractText && preContractText.trim()) {
        const preMessage: ChatMessage = {
          id: `pre-${Date.now()}`,
          role: MessageRole.MODEL,
          content: preContractText,
          isContract: false
        };
        setMessages(prev => [...prev, preMessage]);
      }

      setMessages(prev => [...prev, documentMessage]);

      // Update conversation state to completed
      setConversationState(prev => ({
        ...prev,
        currentStage: ConversationStage.COMPLETED,
        isComplete: true
      }));

      // Auto-open modal if it's a contract
      if (isContract) {
        setReviewModalContent(contractText);
      }

    } catch (error) {
      console.error('Immediate generation failed:', error);
      setError('Failed to generate document. Please try again.');
    } finally {
      setIsLoading(false);
      setCanCancelGeneration(false);
      setGenerationProgress(0);
      setEstimatedTimeRemaining(null);
    }
  };

  // Standard response handler (existing logic)
  const handleStandardResponse = async (prompt: string) => {
    setIsTyping(false);

    const modelPlaceholder: ChatMessage = { id: `${Date.now()}-streaming`, role: MessageRole.MODEL, content: '' };
    setMessages(prev => [...prev, modelPlaceholder]);

    // Use the new conversational API endpoint
    try {
      const conversationHistory = messages
        .filter(msg => msg.role === MessageRole.USER || msg.role === MessageRole.MODEL)
        .map(msg => ({ role: msg.role, content: msg.content }));

      const json = await apiFetch<{
        text: string;
        nextStage?: string;
        extractedData?: Record<string, any>;
        messageType?: string;
        suggestedResponses?: string[];
        requiresConfirmation?: boolean;
      }>(`/api/ai/chat/conversational`, {
        method: 'POST',
        body: JSON.stringify({
          prompt,
          conversationHistory,
          stage: conversationState.currentStage,
          collectedData: conversationState.collectedData,
          assistanceType,
          tone
        })
      });

      const response = json.text || '';

      // Update conversation state if provided
      if (json.nextStage) {
        setConversationState(prev => ({
          ...prev,
          currentStage: json.nextStage as ConversationStage,
          collectedData: { ...prev.collectedData, ...json.extractedData }
        }));
      }

      // Extract contract content and handle contract generation
      const { cleaned, hadMarkers, preContractText, hasStageMarker } = extractDocumentFromResponse(response);
      let isContract = hadMarkers;
      let contractText = hadMarkers ? cleaned : '';

      // Replace placeholder with enhanced message(s)
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMsgIndex = newMessages.length - 1;

        // Remove the placeholder
        if (lastMsgIndex >= 0 && newMessages[lastMsgIndex].role === MessageRole.MODEL) {
          newMessages.splice(lastMsgIndex, 1);
        }

        // Handle stage marker responses (multi-part)
        if (hasStageMarker) {
          // Add pre-contract text as separate message if it exists
          if (preContractText && preContractText.trim()) {
            newMessages.push({
              id: `msg-${Date.now()}-stage1`,
              role: MessageRole.MODEL,
              content: preContractText,
              isContract: false
            });
          }

          // Add the main content (Stage 2)
          if (cleaned && cleaned.trim()) {
            isContract = hadMarkers;
            contractText = hadMarkers ? cleaned : '';
            newMessages.push({
              id: `msg-${Date.now()}-stage2-main`,
              role: MessageRole.MODEL,
              content: cleaned,
              isContract: hadMarkers,
              messageType: json.messageType as any,
              suggestedResponses: json.suggestedResponses,
              requiresConfirmation: json.requiresConfirmation,
              conversationData: json.extractedData
            });
          }
        } else {
          // No stage marker - process as single response

          // Add pre-contract text as separate message if it exists
          if (preContractText && preContractText.trim()) {
            newMessages.push({
              id: `msg-${Date.now()}-pre`,
              role: MessageRole.MODEL,
              content: preContractText,
              isContract: false
            });
          }

          // Add the main content
          if (cleaned && cleaned.trim()) {
            if (hadMarkers) {
              isContract = true;
              contractText = cleaned;
            }
            newMessages.push({
              id: `msg-${Date.now()}-main`,
              role: MessageRole.MODEL,
              content: cleaned,
              isContract: hadMarkers,
              messageType: json.messageType as any,
              suggestedResponses: json.suggestedResponses,
              requiresConfirmation: json.requiresConfirmation,
              conversationData: json.extractedData
            });
          } else if (response.trim()) {
            // Fallback to original response if extraction failed
            newMessages.push({
              id: `msg-${Date.now()}`,
              role: MessageRole.MODEL,
              content: response,
              isContract: false,
              messageType: json.messageType as any,
              suggestedResponses: json.suggestedResponses,
              requiresConfirmation: json.requiresConfirmation,
              conversationData: json.extractedData
            });
          }
        }

        return newMessages;
      });

      // Auto-open modal logic: if markers present (isContract)
      if (isContract) {
        setReviewModalContent(contractText);
      }

    } catch (error) {
      console.error('Conversational API failed, falling back to standard API:', error);

      try {
        // Fallback to standard API
        const json = await apiFetch<{ text: string }>(`/api/ai/chat`, {
          method: 'POST',
          body: JSON.stringify({ prompt })
        });

        const response = json.text || '';

        // Extract contract content and handle contract generation for fallback too
        const { cleaned, hadMarkers, preContractText, hasStageMarker } = extractDocumentFromResponse(response);
        let isContract = hadMarkers;
        let contractText = hadMarkers ? cleaned : '';

        setMessages(prev => {
          const newMessages = [...prev];
          const lastMsgIndex = newMessages.length - 1;

          // Remove the placeholder
          if (lastMsgIndex >= 0 && newMessages[lastMsgIndex].role === MessageRole.MODEL) {
            newMessages.splice(lastMsgIndex, 1);
          }

          // Handle stage marker responses (multi-part)
          if (hasStageMarker) {
            // Add pre-contract text as separate message if it exists
            if (preContractText && preContractText.trim()) {
              newMessages.push({
                id: `msg-${Date.now()}-stage1`,
                role: MessageRole.MODEL,
                content: preContractText,
                isContract: false
              });
            }

            // Add the main content (Stage 2)
            if (cleaned && cleaned.trim()) {
              isContract = hadMarkers;
              contractText = hadMarkers ? cleaned : '';
              newMessages.push({
                id: `msg-${Date.now()}-stage2-main`,
                role: MessageRole.MODEL,
                content: cleaned,
                isContract: hadMarkers
              });
            }
          } else {
            // No stage marker - process as single response

            // Add pre-contract text as separate message if it exists
            if (preContractText && preContractText.trim()) {
              newMessages.push({
                id: `msg-${Date.now()}-pre`,
                role: MessageRole.MODEL,
                content: preContractText,
                isContract: false
              });
            }

            // Add the main content
            if (cleaned && cleaned.trim()) {
              if (hadMarkers) {
                isContract = true;
                contractText = cleaned;
              }
              newMessages.push({
                id: `msg-${Date.now()}-main`,
                role: MessageRole.MODEL,
                content: cleaned,
                isContract: hadMarkers
              });
            } else if (response.trim()) {
              // Fallback to original response if extraction failed
              newMessages.push({
                id: `msg-${Date.now()}`,
                role: MessageRole.MODEL,
                content: response,
                isContract: false
              });
            }
          }

          return newMessages;
        });

        // Auto-open modal logic: if markers present (isContract)
        if (isContract) {
          setReviewModalContent(contractText);
        }
      } catch (fallbackError) {
        console.error('Both APIs failed:', fallbackError);

        // Show error message to user
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMsg = newMessages[newMessages.length - 1];
          if (lastMsg && lastMsg.role === MessageRole.MODEL) {
            lastMsg.content = 'I apologize, but I encountered an error while processing your request. Please try again.';
          }
          return newMessages;
        });

        setError('Failed to generate response. Please try again.');
      }
    }

    setIsLoading(false);
  };

  // Helper function to render different message types
  const renderMessage = (msg: ChatMessage, index: number) => {
    // Handle typing indicator
    if (msg.isTyping) {
      return (
        <div key={msg.id} className="flex items-start gap-4">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
          <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
            <div className="flex items-center gap-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce"></div>
              </div>
              <span className="text-sm text-zinc-500 dark:text-zinc-400">Lexi is thinking...</span>
            </div>
          </div>
        </div>
      );
    }

    // Handle status messages
    if (msg.messageType === 'status') {
      return (
        <div key={msg.id} className="flex items-start gap-4">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
          <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
            <div className="flex items-center gap-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"></div>
              </div>
              <span className="text-sm text-zinc-600 dark:text-zinc-400">{msg.content}</span>
            </div>
          </div>
        </div>
      );
    }

    // Handle interactive messages (questions, confirmations)
    if (msg.messageType === 'question' || msg.messageType === 'confirmation') {
      return (
        <div key={msg.id} className="flex items-start gap-4 animate-fade-in">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 transform transition-all duration-300 hover:scale-[1.01]">
              <MarkdownRenderer content={msg.content} />
            </div>

            {/* Show collected data summary if available */}
            {msg.conversationData && Object.keys(msg.conversationData).length > 0 && (
              <div className="mt-3 p-3 bg-brand-50 dark:bg-brand-900/20 rounded-lg border border-brand-200 dark:border-brand-800">
                <h4 className="text-sm font-medium text-brand-700 dark:text-brand-300 mb-2">
                  Collected Information:
                </h4>
                <div className="space-y-1 text-sm text-brand-600 dark:text-brand-400">
                  {Object.entries(msg.conversationData).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <span className="font-medium">{Array.isArray(value) ? value.join(', ') : String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Suggested responses */}
            {msg.suggestedResponses && msg.suggestedResponses.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2 animate-slide-up">
                {msg.suggestedResponses.map((response, idx) => (
                  <button
                    key={idx}
                    onClick={() => handleSuggestedResponse(response)}
                    className="px-3 py-2 text-sm bg-white dark:bg-zinc-700 border border-zinc-300 dark:border-zinc-600 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-600 transition-all duration-200 hover:scale-105 hover:shadow-md"
                    style={{ animationDelay: `${idx * 100}ms` }}
                  >
                    {response}
                  </button>
                ))}
              </div>
            )}

            {/* Confirmation buttons */}
            {msg.requiresConfirmation && (
              <div className="mt-3 flex gap-2 animate-slide-up">
                <button
                  onClick={() => handleConfirmation(msg.id)}
                  className="px-4 py-2 bg-brand-600 text-white rounded-lg hover:bg-brand-700 transition-all duration-200 hover:scale-105 hover:shadow-lg flex items-center gap-2"
                >
                  <span>✓</span>
                  Continue
                </button>
                <button
                  onClick={() => handleModification(msg.id)}
                  className="px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-all duration-200 hover:scale-105 flex items-center gap-2"
                >
                  <span>✏️</span>
                  Modify
                </button>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Default message rendering (existing logic)
    return (
      <div key={msg.id} className={`flex items-start gap-4 ${msg.role === MessageRole.USER ? 'justify-end' : ''}`}>
        {msg.role === MessageRole.MODEL && (
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
        )}
        <div className={`w-full max-w-xl ${msg.role === MessageRole.USER ? 'order-2' : ''}`}>
          {msg.isContract ? (
            <div className="bg-zinc-50 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4">
              <div className="flex items-center text-zinc-700 dark:text-zinc-200 font-semibold mb-3">
                <DocumentIcon className="w-5 h-5 mr-2" />
                <span>Generated Legal Document</span>
              </div>
              <div className="max-h-48 overflow-y-auto p-2 bg-white dark:bg-zinc-900 rounded border border-zinc-200 dark:border-zinc-700 text-sm text-zinc-800 dark:text-zinc-300">
                <MarkdownRenderer content={msg.content.split('\n').slice(0, 10).join('\n') + "\n..."} />
              </div>
              <div className="mt-3">
                <button
                  onClick={() => setReviewModalContent(msg.content)}
                  className="w-full text-center px-4 py-2 text-sm font-semibold text-white bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm"
                >
                  Review & Save
                </button>
              </div>
            </div>
          ) : (
            <div className={`px-5 py-3 rounded-2xl ${msg.role === MessageRole.USER ? 'bg-brand-600 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200'}`}>
              <MarkdownRenderer content={msg.content} />
              {isLoading && index === messages.length - 1 && <span className="inline-block w-2 h-4 bg-zinc-600 dark:bg-zinc-400 animate-pulse ml-1" />}
            </div>
          )}
        </div>
        {msg.role === MessageRole.USER && (
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center order-1">
            <UserIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
          </div>
        )}
      </div>
    );
  };

  const handleSendMessage = useCallback(async (prompt: string) => {
    if (!prompt.trim() || isLoading) {
      return;
    }

    if (atQuotaLimit) {
      setError(isAnonymous ? "You have reached your free generation limit for this session." : "You have reached your monthly generation limit.");
      return;
    }

    const userMessage: ChatMessage = { id: Date.now().toString(), role: MessageRole.USER, content: prompt };

    // Add user message immediately
    setMessages(prev => prev.length === 1 && prev[0].id === 'initial-message' ? [userMessage] : [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);

    // Show typing indicator
    setIsTyping(true);

    // Add a small delay to show typing indicator
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (loadingTimeoutRef.current) { window.clearTimeout(loadingTimeoutRef.current); }
    loadingTimeoutRef.current = window.setTimeout(() => {
      setIsLoading(false);
      setIsTyping(false);
    }, 60000);

    // Determine if this should be a conversational response
    const shouldUseConversationalFlow = isContractRequest(prompt) && conversationState.currentStage === ConversationStage.INITIAL;

    if (shouldUseConversationalFlow) {
      await handleConversationalFlow(prompt);
    } else {
      await handleStandardResponse(prompt);
    }

    // The conversational flow and standard response handlers already handle the API call and response
    // No need for additional processing here - they manage their own state and cleanup

  }, [isLoading, atQuotaLimit, isAnonymous]);

  const initialPromptHandledRef = useRef(false);

  // Reset the ref when initialPrompt changes
  useEffect(() => {
    initialPromptHandledRef.current = false;
  }, [initialPrompt]);

  useEffect(() => {
    if (initialPrompt && onInitialPromptHandled && !initialPromptHandledRef.current) {
      initialPromptHandledRef.current = true;
      handleSendMessage(initialPrompt);
      onInitialPromptHandled();
    }
  }, [initialPrompt, onInitialPromptHandled]); // Removed handleSendMessage from dependencies

  const handleSaveFromModal = (documentName: string, folderId: string | null, htmlContent: string, clientId: string | null) => {
    if (isAnonymous) {
      setAnonymousQuota(prev => prev - 1);
      // Anonymous user saved document, quota decremented
    } else if (onSaveDocument) {
      const tags: string[] = [];
      // If the flow originated from a template selection, tag as template-origin
      if (fromTemplate) { tags.push('origin:template'); }
      onSaveDocument(htmlContent, documentName, folderId, clientId, { tags });
    }

    setReviewModalContent(null);
  };

  // Helper to fetch the latest assistant message and determine if it's reviewable
  const lastModelMessage: ChatMessage | undefined = (() => {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === MessageRole.MODEL) { return messages[i]; }
    }
    return undefined;
  })();
  const lastText = lastModelMessage?.content || '';
  const lastLooksHtml = /<h\d|<p|<ul|<ol|<table|<section/i.test(lastText);
  const lastIsLarge = lastText.replace(/\s+/g, ' ').length > 1200;
  const canReviewNow = !!lastModelMessage && (lastModelMessage.isContract || lastLooksHtml || lastIsLarge);

  if (isDemo) {
    const demoMessages: ChatMessage[] = [
      { id: 'demo1', role: MessageRole.USER, content: "Draft a simple Non-Disclosure Agreement for me." },
      { id: 'demo2', role: MessageRole.MODEL, content: "Of course, I can help with that. Generating the NDA now..." },
      { id: 'demo3', role: MessageRole.MODEL, content: `<h1>Mutual Non-Disclosure Agreement</h1><p>This Agreement is made between Party A and Party B...</p>`, isContract: true },
    ];
    return (
      <div className="flex flex-col h-[500px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-2xl shadow-2xl shadow-zinc-200/50 dark:shadow-black/20">
        <div className="flex-1 overflow-y-auto p-6 space-y-6 pointer-events-none">
          {demoMessages.map((msg) => (
            <div key={msg.id} className={cn('flex items-start gap-4', msg.role === MessageRole.USER ? 'justify-end' : '')}>
              {msg.role === MessageRole.MODEL && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
                  <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
                </div>
              )}
              <div className={cn('w-full max-w-xl', msg.role === MessageRole.USER ? 'order-2' : '')}>
                {msg.isContract ? (
                  <div className="bg-zinc-50 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4">
                    <div className="flex items-center text-zinc-700 dark:text-zinc-200 font-semibold mb-3">
                      <DocumentIcon className="w-5 h-5 mr-2" />
                      <span>Generated Legal Document</span>
                    </div>
                    <div className="max-h-48 overflow-y-auto p-2 bg-white dark:bg-zinc-900 rounded border border-zinc-200 dark:border-zinc-700 text-sm text-zinc-800 dark:text-zinc-300">
                      <MarkdownRenderer content={msg.content.split('\n').slice(0, 10).join('\n') + "\n..."} />
                    </div>
                    <div className="mt-3">
                      <button className="w-full text-center px-4 py-2 text-sm font-semibold text-white bg-brand-600 rounded-lg shadow-sm">
                        Review & Save
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className={cn('px-5 py-3 rounded-2xl', msg.role === MessageRole.USER ? 'bg-brand-600 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200')}>
                    <MarkdownRenderer content={msg.content} />
                  </div>
                )}
              </div>
              {msg.role === MessageRole.USER && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center order-1">
                  <UserIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }


  // Create quota text JSX for the side panel
  const quotaText = atQuotaLimit ? (
    isAnonymous ? (
      <span>
        Quota limit reached. {setView && <button onClick={() => setView('auth')} className="font-medium text-brand-600 hover:text-brand-700">Sign up</button>} for more generations.
      </span>
    ) : (
      <span>Monthly quota reached. Upgrade to Premium for unlimited generations.</span>
    )
  ) : (
    <span>
      {isPaid ? 'Unlimited generations available.' : `You have ${remaining} generation${remaining !== 1 ? 's' : ''} remaining.`}
    </span>
  );

  return (
    <div className="relative flex h-[70vh] max-h-[700px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-2xl shadow-2xl shadow-zinc-200/50 dark:shadow-black/20">
      <SidePanel
        suggestedPrompts={SUGGESTED_PROMPTS}
        onPromptClick={handleSendMessage}
        quotaText={quotaText}
        isLoading={isLoading}
        atQuotaLimit={atQuotaLimit}
        isExpanded={isSidePanelExpanded}
        onToggle={() => setIsSidePanelExpanded(!isSidePanelExpanded)}
      />
      <div className={cn(
        "flex flex-col flex-1 min-w-0 transition-all duration-300",
        isSidePanelExpanded ? "ml-72" : "ml-0"
      )}>
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Enhanced progress indicator for immediate generation */}
          {conversationState.currentStage !== ConversationStage.INITIAL && (
            <div className="mb-4 p-4 bg-brand-50 dark:bg-brand-900/20 rounded-lg border border-brand-200 dark:border-brand-800">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-brand-700 dark:text-brand-300">
                  Document Generation Progress
                </span>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-brand-600 dark:text-brand-400">
                    {generationProgress > 0 ? generationProgress : getStageProgress(conversationState.currentStage)}% Complete
                  </span>
                  {canCancelGeneration && (
                    <button
                      onClick={handleCancelGeneration}
                      className="text-xs px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                    >
                      Cancel
                    </button>
                  )}
                </div>
              </div>
              <div className="w-full bg-brand-200 dark:bg-brand-800 rounded-full h-2">
                <div
                  className="bg-brand-600 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${generationProgress > 0 ? generationProgress : getStageProgress(conversationState.currentStage)}%` }}
                ></div>
              </div>
              <div className="mt-2 flex items-center justify-between">
                <div className="text-xs text-brand-600 dark:text-brand-400">
                  {getStageDescription(conversationState.currentStage)}
                </div>
                {estimatedTimeRemaining !== null && estimatedTimeRemaining > 0 && (
                  <div className="text-xs text-brand-500 dark:text-brand-500">
                    ~{Math.ceil(estimatedTimeRemaining)}s remaining
                  </div>
                )}
              </div>
            </div>
          )}

          {messages.map((msg, index) => renderMessage(msg, index))}
          {isTyping && (
            <div className="flex items-start gap-4 animate-fade-in">
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
                <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
              </div>
              <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
                <div className="flex items-center gap-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce"></div>
                  </div>
                  <span className="text-sm text-zinc-500 dark:text-zinc-400">Lexi is thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="p-4 border-t border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 rounded-b-2xl">

          {/* Integrated Message Input Area */}
          <div className="w-full">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    // Allow Enter to create new lines, don't submit
                    return;
                  }
                }}
                placeholder={atQuotaLimit ? (isAnonymous ? "Sign up to continue." : "Upgrade to continue.") : "Describe the contract you need..."}
                className="w-full px-4 pt-3 pb-12 bg-zinc-100 dark:bg-zinc-800 rounded-xl focus:outline-none focus:ring-2 focus:ring-brand-500 transition disabled:opacity-50 resize-none min-h-[80px] max-h-40"
                disabled={isLoading || atQuotaLimit}
                rows={1}
              />

              {/* Bottom Controls Overlay */}
              <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between gap-2 z-10">
                {/* Left side: Dropdowns */}
                <div className="flex items-center gap-2">
                  {/* Assistance Type Dropdown */}
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-zinc-500 dark:text-zinc-400 whitespace-nowrap">Assistance Type:</span>
                    <Dropdown>
                      <Dropdown.Trigger>
                        <div className="px-2 py-1 bg-white dark:bg-zinc-900 rounded text-xs text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-600 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors shadow-sm">
                          {assistanceType}
                        </div>
                      </Dropdown.Trigger>
                      <Dropdown.Content>
                        <Dropdown.Item onClick={() => setAssistanceType('Draft Contract')}>
                          Draft Contract
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setAssistanceType('Suggest Clause')}>
                          Suggest Clause
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setAssistanceType('Legal Assistance')}>
                          Legal Assistance
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setAssistanceType('General Legal Query')}>
                          General Legal Query
                        </Dropdown.Item>
                      </Dropdown.Content>
                    </Dropdown>
                  </div>

                  {/* Tone Dropdown */}
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-zinc-500 dark:text-zinc-400 whitespace-nowrap">Tone:</span>
                    <Dropdown>
                      <Dropdown.Trigger>
                        <div className="px-2 py-1 bg-white dark:bg-zinc-900 rounded text-xs text-zinc-700 dark:text-zinc-300 border border-zinc-300 dark:border-zinc-600 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors shadow-sm">
                          {tone}
                        </div>
                      </Dropdown.Trigger>
                      <Dropdown.Content>
                        <Dropdown.Item onClick={() => setTone('Professional')}>
                          Professional
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setTone('Casual')}>
                          Casual
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setTone('Friendly')}>
                          Friendly
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setTone('Formal')}>
                          Formal
                        </Dropdown.Item>
                      </Dropdown.Content>
                    </Dropdown>
                  </div>
                </div>

                {/* Right side: Action Buttons */}
                <div className="flex items-center gap-1">
                  {/* Microphone Button */}
                  <button
                    type="button"
                    onClick={isRecording ? stopRecording : startRecording}
                    className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center transition-colors",
                      isRecording
                        ? "bg-red-500 text-white hover:bg-red-600"
                        : "bg-zinc-200 dark:bg-zinc-700 text-zinc-600 dark:text-zinc-300 hover:bg-zinc-300 dark:hover:bg-zinc-600"
                    )}
                    disabled={isLoading || atQuotaLimit}
                    title={isRecording ? "Stop recording" : "Start voice input"}
                  >
                    {isRecording ? (
                      <MicrophoneSlashIcon className="w-4 h-4" />
                    ) : (
                      <MicrophoneIcon className="w-4 h-4" />
                    )}
                  </button>

                  {/* Send Button */}
                  {!isLoading && (
                    <button
                      type="button"
                      onClick={() => handleSendMessage(input)}
                      className="w-8 h-8 bg-brand-600 text-white rounded-full flex items-center justify-center hover:bg-brand-700 disabled:bg-zinc-300 dark:disabled:bg-zinc-700 transition-colors shadow-sm"
                      disabled={isLoading || !input.trim() || atQuotaLimit}
                      title="Send message"
                    >
                      <SendIcon className="w-4 h-4" />
                    </button>
                  )}

                  {/* Stop Button (only when loading) */}
                  {isLoading && (
                    <button
                      type="button"
                      onClick={() => { abortRef.current?.abort(); setIsLoading(false); }}
                      className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors shadow-sm"
                      title="Stop generation"
                    >
                      <StopIcon className="w-4 h-4" />
                    </button>
                  )}

                  {/* Review & Save Button */}
                  {!isLoading && (
                    <button
                      type="button"
                      onClick={() => canReviewNow && setReviewModalContent(lastText)}
                      disabled={!canReviewNow}
                      className={cn(
                        'px-2 py-1 rounded text-xs border transition-colors shadow-sm mr-2',
                        canReviewNow
                          ? 'border-brand-600 text-brand-700 bg-white hover:bg-brand-50 dark:text-brand-400 dark:bg-zinc-900 dark:hover:bg-brand-900/30'
                          : 'border-zinc-300 dark:border-zinc-600 text-zinc-400 bg-white dark:bg-zinc-900 cursor-not-allowed'
                      )}
                      title={canReviewNow ? 'Open Review & Save' : 'No document to review yet'}
                    >
                      Review & Save
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>



          {error && <p className="text-red-500 text-sm text-center mt-2">{error}</p>}
        </div>
      </div>

      <DocumentViewerModal
        isOpen={!!reviewModalContent}
        onClose={() => setReviewModalContent(null)}
        contractContent={reviewModalContent || ''}
        onSave={handleSaveFromModal}
        user={user}
      />
    </div >
  );
});

export default ChatInterface;
