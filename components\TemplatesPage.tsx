

import React, { memo, useMemo, useState } from 'react';
import { cn } from '../lib/utils';
import { CustomTemplate, DashboardView, Template, User } from '../types';
import { LockSolidIcon, TemplateIcon, TrashIcon } from './Icons';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader } from './ui/Card';
import { Ta<PERSON>, TabsList, TabsTrigger } from './ui/Tabs';

interface TemplatesPageProps {
  user: User;
  onSelectTemplate: (prompt: string) => void;
  setView: (view: DashboardView) => void;
  onDeleteCustomTemplate: (templateId: string) => void;
  publicTemplates: Template[];
}

interface TemplateCardProps {
  template: Template;
  isLocked: boolean;
  onUse: () => void;
  onUpgrade: () => void;
}

const PublicTemplateCard: React.FC<TemplateCardProps> = memo(({ template, isLocked, onUse, onUpgrade }) => {
  return (
    <Card
      className={cn(
        "flex flex-col transition-all",
        isLocked ? "bg-zinc-50 dark:bg-zinc-900/50" : "hover:shadow-xl dark:hover:border-brand-500/50"
      )}
    >
      <div className="p-6 flex-1 flex flex-col">
        <div className="flex justify-between items-start">
          <h3 className="font-semibold text-zinc-900 dark:text-zinc-100">{template.title}</h3>
          {isLocked && (
            <span className="flex-shrink-0 ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300">
              <LockSolidIcon className="w-3 h-3 mr-1.5" />
              Premium
            </span>
          )}
        </div>
        <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400 flex-1">{template.description}</p>
      </div>
      <div className="p-4 border-t border-zinc-200 dark:border-zinc-800 bg-zinc-50/50 dark:bg-zinc-900 rounded-b-xl">
        <Button
          className="w-full"
          variant={isLocked ? 'outline' : 'default'}
          onClick={isLocked ? onUpgrade : onUse}
        >
          {isLocked ? 'Upgrade to Use' : 'Use Template'}
        </Button>
      </div>
    </Card>
  );
});

const CustomTemplateCard: React.FC<{ template: CustomTemplate, onUse: () => void, onDelete: () => void }> = memo(({ template, onUse, onDelete }) => (
  <Card className="flex flex-col transition-all hover:shadow-xl dark:hover:border-brand-500/50">
    <div className="p-6 flex-1 flex flex-col">
      <h3 className="font-semibold text-zinc-900 dark:text-zinc-100">{template.name}</h3>
      <p className="mt-2 text-sm text-zinc-500 dark:text-zinc-400">Created on {new Date(template.createdAt).toLocaleDateString()}</p>
    </div>
    <div className="p-4 border-t border-zinc-200 dark:border-zinc-800 bg-zinc-50/50 dark:bg-zinc-900 rounded-b-xl flex items-center gap-2">
      <Button className="w-full" onClick={onUse}>Use Template</Button>
      <Button variant="outline" size="icon" className="flex-shrink-0" onClick={onDelete}><TrashIcon className="w-4 h-4 text-red-600 dark:text-red-500" /></Button>
    </div>
  </Card>
));




const TemplatesPage: React.FC<TemplatesPageProps> = memo(({ user, onSelectTemplate, setView, onDeleteCustomTemplate, publicTemplates }) => {
  const [activeTab, setActiveTab] = useState('library');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = useMemo(() => {
    const allCategories = new Set(publicTemplates.map(t => t.category));
    return ['All', ...Array.from(allCategories)];
  }, [publicTemplates]);

  const filteredTemplates = useMemo(() => {
    if (selectedCategory === 'All') {
      return publicTemplates;
    }
    return publicTemplates.filter(t => t.category === selectedCategory);
  }, [selectedCategory, publicTemplates]);

  const groupedTemplates = useMemo(() => {
    return filteredTemplates.reduce((acc, template) => {
      (acc[template.category] = acc[template.category] || []).push(template);
      return acc;
    }, {} as Record<string, Template[]>);
  }, [filteredTemplates]);

  const planRank = { 'Registered User': 1, 'Premium': 2, 'Enterprise': 3 };
  const userPlanRank = planRank[user.planName as keyof typeof planRank] || 0;
  const isPremiumFeatureEnabled = userPlanRank >= 2;

  const renderPublicTemplateCard = (template: Template) => {
    const requiredPlanRank = planRank[template.requiredPlan as keyof typeof planRank];
    const isLocked = userPlanRank < requiredPlanRank;
    return (
      <PublicTemplateCard
        key={template.id}
        template={template}
        isLocked={isLocked}
        onUse={() => onSelectTemplate(template.prompt)}
        onUpgrade={() => setView('subscription')}
      />
    );
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Templates</h1>
        <p className="text-zinc-600 dark:text-zinc-400 mt-1">Browse our library of professionally crafted legal templates or create your own to get started quickly.</p>
      </div>

      <Tabs>
        <TabsList>
          <TabsTrigger onClick={() => setActiveTab('library')} data-state={activeTab === 'library' ? 'active' : ''}>Template Library</TabsTrigger>
          <TabsTrigger onClick={() => setActiveTab('custom')} data-state={activeTab === 'custom' ? 'active' : ''}>My Templates</TabsTrigger>
        </TabsList>

        {activeTab === 'library' && (
          <Card className="mt-4">
            <CardHeader>
              <div className="flex flex-wrap items-center gap-2">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </CardHeader>
            <CardContent>
              {selectedCategory === 'All' ? (
                <div className="space-y-8">
                  {(Object.entries(groupedTemplates) as [string, Template[]][]).map(([category, templates]) => {


                    return (
                      <div key={category}>
                        <h2 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200 mb-4">{category}</h2>
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                          {templates.map(renderPublicTemplateCard)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                (() => {


                  return (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {filteredTemplates.length > 0 ? filteredTemplates.map(renderPublicTemplateCard) : <p>No templates found.</p>}
                    </div>
                  );
                })()
              )}
            </CardContent>
          </Card>
        )}

        {activeTab === 'custom' && (
          <Card className="mt-4">
            <CardHeader>
              <h2 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">My Templates</h2>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">Your personal library of saved templates.</p>
            </CardHeader>
            <CardContent>
              {!isPremiumFeatureEnabled ? (
                <div className="text-center py-12">
                  <LockSolidIcon className="w-10 h-10 mx-auto text-zinc-300 dark:text-zinc-700 mb-4" />
                  <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Unlock Custom Templates</h3>
                  <p className="text-sm text-zinc-500 dark:text-zinc-400 mt-2 mb-4">Upgrade to Premium to save and reuse your own templates.</p>
                  <Button onClick={() => setView('subscription')}>
                    Upgrade Now
                  </Button>
                </div>
              ) : (
                (user.customTemplates || []).length > 0 ? (
                  (() => {
                    const customTemplates = user.customTemplates || [];


                    return (
                      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                        {customTemplates.map(template => (
                          <CustomTemplateCard
                            key={template.id}
                            template={template}
                            onUse={() => onSelectTemplate(template.content)}
                            onDelete={() => onDeleteCustomTemplate(template.id)}
                          />
                        ))}
                      </div>
                    );
                  })()
                ) : (
                  <div className="text-center py-16">
                    <TemplateIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                    <h3 className="mt-2 text-lg font-semibold text-zinc-800 dark:text-zinc-200">No Custom Templates</h3>
                    <p className="mt-1 text-sm text-zinc-500 dark:text-zinc-400">Save a document as a template from the editor to get started.</p>
                  </div>
                )
              )}
            </CardContent>
          </Card>
        )}
      </Tabs>
    </div>
  );
});

export default TemplatesPage;
