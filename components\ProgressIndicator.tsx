import React from 'react';
import { cn } from '../lib/utils';
import { ConversationProgress, ConversationStage } from '../types';
import { CheckIcon } from './Icons';

interface ProgressIndicatorProps {
  currentStage: ConversationStage;
  className?: string;
}

const PROGRESS_STAGES: ConversationProgress[] = [
  {
    stage: ConversationStage.ANALYZING_REQUEST,
    stageLabel: 'Analyzing Request',
    percentage: 10,
    statusMessage: 'Understanding your needs',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.PROCESSING_REQUEST,
    stageLabel: 'Processing Request',
    percentage: 20,
    statusMessage: 'Processing requirements',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.APPLYING_DEFAULTS,
    stageLabel: 'Applying Defaults',
    percentage: 35,
    statusMessage: 'Applying best practices',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.PLANNING_STRUCTURE,
    stageLabel: 'Planning Structure',
    percentage: 50,
    statusMessage: 'Designing framework',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.GENERATING_OUTLINE,
    stageLabel: 'Creating Outline',
    percentage: 65,
    statusMessage: 'Creating outline',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.GENERATING_CONTRACT,
    stageLabel: 'Generating Document',
    percentage: 80,
    statusMessage: 'Creating document',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.FINALIZING_DOCUMENT,
    stageLabel: 'Finalizing',
    percentage: 95,
    statusMessage: 'Finalizing document',
    isActive: false,
    isCompleted: false
  },
  {
    stage: ConversationStage.REVIEW_READY,
    stageLabel: 'Ready for Review',
    percentage: 100,
    statusMessage: 'Complete',
    isActive: false,
    isCompleted: false
  }
];

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ currentStage, className }) => {
  const currentStageIndex = PROGRESS_STAGES.findIndex(stage => stage.stage === currentStage);
  const currentProgress = currentStageIndex >= 0 ? PROGRESS_STAGES[currentStageIndex].percentage : 0;

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      {/* Progress bar */}
      <div className="relative mb-4">
        <div className="w-full bg-zinc-200 dark:bg-zinc-700 rounded-full h-2">
          <div
            className="bg-brand-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${currentProgress}%` }}
          />
        </div>
        <div className="absolute -top-1 right-0 text-xs text-zinc-500 dark:text-zinc-400">
          {currentProgress}%
        </div>
      </div>

      {/* Stage indicators */}
      <div className="flex justify-between items-center">
        {PROGRESS_STAGES.map((stage, index) => {
          const isCompleted = index < currentStageIndex;
          const isActive = index === currentStageIndex;
          const isUpcoming = index > currentStageIndex;

          return (
            <div key={stage.stage} className="flex flex-col items-center">
              {/* Stage circle */}
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300",
                isCompleted && "bg-brand-500 text-white",
                isActive && "bg-brand-100 dark:bg-brand-900 border-2 border-brand-500 text-brand-700 dark:text-brand-400",
                isUpcoming && "bg-zinc-200 dark:bg-zinc-700 text-zinc-400"
              )}>
                {isCompleted ? (
                  <CheckIcon className="w-4 h-4" />
                ) : (
                  <span className="text-xs font-medium">{index + 1}</span>
                )}
              </div>

              {/* Stage label */}
              <span className={cn(
                "text-xs mt-2 text-center max-w-16 leading-tight",
                isActive && "text-brand-700 dark:text-brand-400 font-medium",
                isCompleted && "text-zinc-600 dark:text-zinc-400",
                isUpcoming && "text-zinc-400 dark:text-zinc-500"
              )}>
                {stage.stageLabel}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProgressIndicator;
