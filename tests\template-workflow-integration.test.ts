import { describe, it, expect, vi, beforeEach } from 'vitest';

describe('Template Workflow Integration', () => {
  let mockHandleSendMessage: ReturnType<typeof vi.fn>;
  let mockOnInitialPromptHandled: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockHandleSendMessage = vi.fn();
    mockOnInitialPromptHandled = vi.fn();
  });

  it('should simulate the template selection to ChatInterface workflow', () => {
    // Step 1: Simulate template selection in TemplatesPage
    const templatePrompt = 'Create a professional Non-Disclosure Agreement (NDA)';
    console.log('[Test] Template selected:', templatePrompt);

    // Step 2: Simulate DashboardLayout handleSelectTemplate
    let promptToGenerate: string | null = null;
    let isTemplateSession = false;
    let dashboardView = 'dashboard';

    const handleSelectTemplate = (prompt: string) => {
      console.log('[Test] handleSelectTemplate called with:', prompt);
      promptToGenerate = prompt;
      isTemplateSession = true;
      dashboardView = 'generate';
    };

    handleSelectTemplate(templatePrompt);

    // Verify state is set correctly
    expect(promptToGenerate).toBe(templatePrompt);
    expect(isTemplateSession).toBe(true);
    expect(dashboardView).toBe('generate');

    // Step 3: Simulate DashboardPage passing props to ChatInterface
    const chatInterfaceProps = {
      initialPrompt: promptToGenerate,
      fromTemplate: !!isTemplateSession,
      onInitialPromptHandled: mockOnInitialPromptHandled
    };

    console.log('[Test] ChatInterface props:', chatInterfaceProps);

    // Step 4: Simulate ChatInterface useEffect logic
    const initialPromptHandledRef = { current: false };

    // Simulate the useEffect condition check
    const shouldExecute = !!(
      chatInterfaceProps.initialPrompt && 
      chatInterfaceProps.onInitialPromptHandled && 
      !initialPromptHandledRef.current
    );

    console.log('[Test] Should execute initial prompt:', shouldExecute);
    expect(shouldExecute).toBe(true);

    // Simulate the useEffect execution
    if (shouldExecute) {
      console.log('[Test] Executing initial prompt:', chatInterfaceProps.initialPrompt);
      initialPromptHandledRef.current = true;
      mockHandleSendMessage(chatInterfaceProps.initialPrompt);
      chatInterfaceProps.onInitialPromptHandled();
    }

    // Verify the workflow executed correctly
    expect(mockHandleSendMessage).toHaveBeenCalledWith(templatePrompt);
    expect(mockOnInitialPromptHandled).toHaveBeenCalled();
    expect(initialPromptHandledRef.current).toBe(true);

    // Step 5: Simulate clearPromptToGenerate (onInitialPromptHandled)
    const clearPromptToGenerate = () => {
      console.log('[Test] clearPromptToGenerate called');
      promptToGenerate = null;
      isTemplateSession = false;
    };

    clearPromptToGenerate();

    // Verify state is cleared correctly
    expect(promptToGenerate).toBe(null);
    expect(isTemplateSession).toBe(false);
  });

  it('should not execute if initialPrompt is null', () => {
    const chatInterfaceProps = {
      initialPrompt: null,
      fromTemplate: false,
      onInitialPromptHandled: mockOnInitialPromptHandled
    };

    const initialPromptHandledRef = { current: false };

    const shouldExecute = !!(
      chatInterfaceProps.initialPrompt && 
      chatInterfaceProps.onInitialPromptHandled && 
      !initialPromptHandledRef.current
    );

    expect(shouldExecute).toBe(false);
    expect(mockHandleSendMessage).not.toHaveBeenCalled();
    expect(mockOnInitialPromptHandled).not.toHaveBeenCalled();
  });

  it('should not execute if onInitialPromptHandled is null', () => {
    const chatInterfaceProps = {
      initialPrompt: 'Some prompt',
      fromTemplate: true,
      onInitialPromptHandled: null
    };

    const initialPromptHandledRef = { current: false };

    const shouldExecute = !!(
      chatInterfaceProps.initialPrompt && 
      chatInterfaceProps.onInitialPromptHandled && 
      !initialPromptHandledRef.current
    );

    expect(shouldExecute).toBe(false);
    expect(mockHandleSendMessage).not.toHaveBeenCalled();
  });

  it('should not execute if already handled', () => {
    const chatInterfaceProps = {
      initialPrompt: 'Some prompt',
      fromTemplate: true,
      onInitialPromptHandled: mockOnInitialPromptHandled
    };

    const initialPromptHandledRef = { current: true }; // Already handled

    const shouldExecute = !!(
      chatInterfaceProps.initialPrompt && 
      chatInterfaceProps.onInitialPromptHandled && 
      !initialPromptHandledRef.current
    );

    expect(shouldExecute).toBe(false);
    expect(mockHandleSendMessage).not.toHaveBeenCalled();
    expect(mockOnInitialPromptHandled).not.toHaveBeenCalled();
  });
});
