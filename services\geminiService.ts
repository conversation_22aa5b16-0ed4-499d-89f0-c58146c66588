import { Chat, GoogleGenAI, Type } from "@google/genai";
import { SYSTEM_PROMPT } from '../constants';
import { AnalysisResult, ExternalAnalysisResult, KeyDate, Obligation, SuggestedClause } from "../types";

// Resolve API key from client or server envs without breaking the browser at import time
const CLIENT_KEY = (typeof import.meta !== 'undefined' && process.env?.VITE_GEMINI_API_KEY) as string | undefined;
const SERVER_KEY = (typeof process !== 'undefined' ? process.env?.GEMINI_API_KEY : undefined) as string | undefined;
const API_KEY = CLIENT_KEY || SERVER_KEY;

let ai: GoogleGenAI | null = null;
function getAI(): GoogleGenAI {
  if (!ai) {
    if (!API_KEY) {
      throw new Error('AI not configured: missing GEMINI API key');
    }
    ai = new GoogleGenAI({ apiKey: API_KEY });
  }
  return ai;
}

// For testing purposes - reset the singleton
export function resetAI(): void {
  ai = null;
}

export function createChatSession(assistanceType?: string, tone?: string): Chat {
  const dynamicSystemPrompt = generateDynamicSystemPrompt(assistanceType, tone);
  const chat = getAI().chats.create({
    model: 'gemini-2.5-flash',
    config: {
      systemInstruction: dynamicSystemPrompt,
    },
  });
  return chat;
}

function generateDynamicSystemPrompt(assistanceType?: string, tone?: string): string {
  let prompt = SYSTEM_PROMPT;

  // Add assistance type specific instructions
  if (assistanceType) {
    prompt += `\n\nCURRENT ASSISTANCE TYPE: ${assistanceType}`;
    switch (assistanceType) {
      case 'Draft Contract':
        prompt += '\nFocus on comprehensive document creation with detailed clauses and legal structure.';
        break;
      case 'Suggest Clause':
        prompt += '\nProvide specific clause recommendations with explanations and legal rationale.';
        break;
      case 'Legal Assistance':
        prompt += '\nOffer general legal guidance, explanations, and educational information.';
        break;
      case 'General Legal Query':
        prompt += '\nAnswer legal questions with clear explanations and practical guidance.';
        break;
    }
  }

  // Add tone specific instructions
  if (tone) {
    prompt += `\n\nCURRENT TONE: ${tone}`;
    switch (tone) {
      case 'Professional':
        prompt += '\nUse formal legal language, precise terminology, and structured responses.';
        break;
      case 'Casual':
        prompt += '\nUse conversational language while maintaining accuracy, with a friendly approach.';
        break;
      case 'Friendly':
        prompt += '\nUse warm, approachable tone with encouragement and supportive language.';
        break;
      case 'Formal':
        prompt += '\nUse highly structured, traditional legal communication style with formal language.';
        break;
    }
  }

  return prompt;
}

export async function sendMessageToChatStream(
    chat: Chat,
    message: string,
    onChunk: (chunk: string) => void
): Promise<void> {
    try {
        const result = await chat.sendMessageStream({ message });
        for await (const chunk of result) {
            onChunk(chunk.text);
        }
    } catch (error) {
        // Error sending message to Gemini
        const errorMessage = error instanceof Error
            ? `An error occurred while communicating with the AI: ${error.message}`
            : "An unknown error occurred while communicating with the AI.";
        onChunk(errorMessage);
    }
}

export async function getSuggestedClauses(documentContent: string): Promise<SuggestedClause[]> {
  try {
    const response = await getAI().models.generateContent({
      model: "gemini-2.5-flash",
      contents: `Based on the following legal document, suggest 3-5 relevant clauses that could be added to improve it. Provide a title, a brief description of what the clause does, and the full legal text for the clause. Here is the document content: \n\n${documentContent}`,
      config: {
        responseMimeType: "application/json",
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            suggestions: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  title: { type: Type.STRING, description: 'The title of the suggested clause.' },
                  description: { type: Type.STRING, description: 'A brief explanation of the clause.' },
                  content: { type: Type.STRING, description: 'The full legal text of the clause, formatted in HTML.' },
                },
                propertyOrdering: ["title", "description", "content"],
              },
            },
          },
        },
      },
    });

    const jsonStr = response.text.trim();
    const parsedJson = JSON.parse(jsonStr);
    return parsedJson.suggestions || [];
  } catch {
    // Error fetching suggested clauses
    return [];
  }
}

export async function analyzeDocument(documentContent: string): Promise<AnalysisResult> {
    try {
        const response = await getAI().models.generateContent({
            model: "gemini-2.5-flash",
            contents: `Analyze the following legal document. Provide a brief overall summary and a list of findings. Findings should include potential issues, unclear language, or missing clauses. For each finding, provide a severity ('High Priority', 'Suggestion', or 'Information') and a description of the issue. Document content:\n\n${documentContent}`,
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        summary: { type: Type.STRING, description: 'A brief overall summary of the document analysis.' },
                        findings: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    severity: { type: Type.STRING, description: "The severity of the finding, e.g., 'High Priority', 'Suggestion'." },
                                    description: { type: Type.STRING, description: 'A detailed explanation of the finding.' },
                                },
                                propertyOrdering: ["severity", "description"],
                            },
                        },
                    },
                },
            },
        });

        const jsonStr = response.text.trim();
        const parsedJson = JSON.parse(jsonStr);
        return parsedJson || { summary: 'No analysis available.', findings: [] };
    } catch {
        // Error analyzing document
        throw new Error("Failed to analyze document.");
    }
}

export async function extractKeyDates(documentContent: string): Promise<KeyDate[]> {
    try {
        const response = await getAI().models.generateContent({
            model: "gemini-2.5-flash",
            contents: `From the following document, extract any key dates such as 'Effective Date', 'End Date', 'Termination Date', or 'Renewal Notice Date'. Return them as a list. For each, provide the event name and the date in YYYY-MM-DD format. Document content:\n\n${documentContent}`,
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        keyDates: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    event: { type: Type.STRING, description: "The name of the event, e.g., 'Contract End Date'." },
                                    date: { type: Type.STRING, description: 'The date of the event in YYYY-MM-DD format.' },
                                },
                                propertyOrdering: ["event", "date"],
                            },
                        },
                    },
                },
            },
        });

        const jsonStr = response.text.trim();
        const parsedJson = JSON.parse(jsonStr);
        // Validate date format before returning
        return (parsedJson.keyDates || []).filter((kd: KeyDate) => kd.date && !isNaN(new Date(kd.date).getTime()));
    } catch {
        // Error extracting key dates
        return [];
    }
}

export async function extractObligations(documentContent: string): Promise<Omit<Obligation, 'id' | 'documentId' | 'status' | 'ownerEmail'>[]> {
    try {
        const response = await getAI().models.generateContent({
            model: "gemini-2.5-flash",
            contents: `As an expert paralegal, analyze the following legal document and extract any key obligations, commitments, or deadlines. For each, provide a concise description of the obligation and its due date in YYYY-MM-DD format. If no specific date is mentioned, calculate it based on the text (e.g., '30 days after the Effective Date'). Assume the Effective Date is today, ${new Date().toISOString().split('T')[0]}. Document content:\n\n${documentContent}`,
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        obligations: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    description: { type: Type.STRING, description: "A concise description of the obligation." },
                                    dueDate: { type: Type.STRING, description: 'The due date of the obligation in YYYY-MM-DD format.' },
                                },
                                propertyOrdering: ["description", "dueDate"],
                            },
                        },
                    },
                },
            },
        });

        const jsonStr = response.text.trim();
        const parsedJson = JSON.parse(jsonStr);
        return (parsedJson.obligations || []).filter((ob: { description: string; dueDate: string }) => ob.dueDate && !isNaN(new Date(ob.dueDate).getTime()));
    } catch {
        // Error extracting obligations
        return [];
    }
}


export async function analyzeExternalDocument(documentContent: string): Promise<ExternalAnalysisResult> {
    try {
        const response = await getAI().models.generateContent({
            model: "gemini-2.5-flash",
            contents: `You are an expert AI legal document analyzer. Your task is to review the provided legal document text and return a structured analysis in JSON format. Analyze the following document: --- ${documentContent} --- Based on your analysis, provide: 1. A brief, neutral summary of the document's purpose. 2. The likely type of document (e.g., "Non-Disclosure Agreement", "Employment Contract"). 3. A list of key clauses, each with a title and a one-sentence summary. 4. A list of potential risks, each with a severity ('High', 'Medium', or 'Low') and a description. 5. A list of actionable suggestions for improvement.`,
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        summary: { type: Type.STRING, description: 'A brief, neutral summary of the document.' },
                        documentType: { type: Type.STRING, description: 'The likely type of legal document.' },
                        keyClauses: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    title: { type: Type.STRING, description: 'The name of the clause.' },
                                    summary: { type: Type.STRING, description: 'A one-sentence summary of the clause.' },
                                },
                            },
                        },
                        risks: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    severity: { type: Type.STRING, description: "'High', 'Medium', or 'Low'." },
                                    description: { type: Type.STRING, description: 'A clear description of the potential risk.' },
                                },
                            },
                        },
                        suggestions: {
                            type: Type.ARRAY,
                            items: { type: Type.STRING },
                            description: 'Actionable suggestions for improvement.',
                        },
                    },
                },
            },
        });

        const jsonStr = response.text.trim();
        const parsedJson = JSON.parse(jsonStr);
        return parsedJson;
    } catch (error) {
        if (error instanceof Error && error.message.toLowerCase().includes('not configured')) {
            throw error;
        }
        // Error analyzing external document
        throw new Error("Failed to analyze the document with AI.");
    }
}

export async function compareDocumentVersions(contentA: string, contentB: string): Promise<string> {
    try {
        const response = await getAI().models.generateContent({
            model: "gemini-2.5-flash",
            contents: `You are a document comparison tool. Compare the two versions of the document provided below. Your goal is to produce a single HTML document that shows the changes from "Version A" to "Version B". Use standard HTML <ins> tags for additions and <del> tags for deletions. Do not add any explanation or commentary outside of the HTML document itself.

--- VERSION A ---
${contentA}
--- END VERSION A ---

--- VERSION B ---
${contentB}
--- END VERSION B ---`,
        });
        return response.text.trim();
    } catch {
        // Error comparing document versions
        return "<p>Error: Could not compare document versions.</p>";
    }
}
