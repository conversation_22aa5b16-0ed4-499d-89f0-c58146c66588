// Test script to verify the document update fix
const fetch = require('node-fetch');

async function testDocumentUpdate() {
  const baseUrl = 'http://localhost:3002';
  
  // This would normally require authentication, but let's test the endpoint structure
  const testDocumentId = '811a5124-2d75-48a4-95c8-548b16e8d467';
  const testClientId = 'd1a750c5-e917-4a3e-82b3-56809acbd2b8';
  
  try {
    console.log('Testing document update endpoint...');
    
    const response = await fetch(`${baseUrl}/api/documents/${testDocumentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        // Note: This will fail without proper auth, but we can see if the error changes
      },
      body: JSON.stringify({
        clientId: testClientId
      })
    });
    
    const result = await response.text();
    console.log('Response status:', response.status);
    console.log('Response body:', result);
    
    if (response.status === 401) {
      console.log('✅ Expected 401 (authentication required) - endpoint is working');
    } else if (response.status === 500) {
      console.log('❌ Still getting 500 error - fix may not be complete');
    } else {
      console.log('✅ Got different status code - fix may be working');
    }
    
  } catch (error) {
    console.error('Error testing endpoint:', error.message);
  }
}

testDocumentUpdate();
