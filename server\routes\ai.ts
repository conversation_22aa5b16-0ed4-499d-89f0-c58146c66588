import { GoogleGenAI, Type } from '@google/genai';
import { Router } from 'express';
import { z } from 'zod';
import { analyzeExternalDocument, createChatSession } from '../../services/geminiService';
import { AuthRequest, maybeAuth, requireAuth } from '../middleware/auth';

const router = Router();


function getAI() {
  const GEMINI_API_KEY = (process.env.GEMINI_API_KEY || '').trim();
  if (!GEMINI_API_KEY || GEMINI_API_KEY === '' || GEMINI_API_KEY === 'undefined') {
    return null;
  }
  return new GoogleGenAI({ apiKey: GEMINI_API_KEY });
}

// Helper function to analyze conversational responses
function analyzeConversationalResponse(response: string, currentStage: string, collectedData: Record<string, any>) {
  const analysis = {
    nextStage: currentStage,
    extractedData: { ...collectedData },
    messageType: 'conversation' as 'conversation' | 'status' | 'question' | 'progress' | 'confirmation' | 'summary' | 'document',
    suggestedResponses: [] as string[],
    requiresConfirmation: false
  };

  // Detect if response contains questions (indicates gathering stage)
  if (response.includes('?') && (response.includes('1.') || response.includes('**1.'))) {
    analysis.messageType = 'question';
    analysis.nextStage = 'gathering_requirements';
    analysis.suggestedResponses = [
      "Let me provide the details",
      "I need help with the requirements",
      "Use standard terms for now"
    ];
  }

  // Detect confirmation requests
  if (response.toLowerCase().includes('confirm') || response.toLowerCase().includes('correct') ||
      response.toLowerCase().includes('proceed')) {
    analysis.messageType = 'confirmation';
    analysis.requiresConfirmation = true;
    analysis.nextStage = 'generating_contract';
  }

  // Detect contract generation
  if (response.includes('---CONTRACT START---')) {
    analysis.messageType = 'document';
    analysis.nextStage = 'review_ready';
  }

  // Extract data from user responses (simple keyword extraction)
  const lowerResponse = response.toLowerCase();

  // Extract parties
  if (lowerResponse.includes('party') || lowerResponse.includes('company') || lowerResponse.includes('individual')) {
    // Simple extraction - in a real implementation, this would be more sophisticated
    const parties = response.match(/([A-Z][a-z]+ [A-Z][a-z]+|[A-Z][a-z]+ Inc\.|[A-Z][a-z]+ LLC)/g);
    if (parties) {
      analysis.extractedData.parties = parties;
    }
  }

  // Extract jurisdiction
  if (lowerResponse.includes('state') || lowerResponse.includes('country') || lowerResponse.includes('jurisdiction')) {
    const jurisdictions = response.match(/(California|New York|Texas|Florida|Illinois|United States|USA|UK|Canada)/gi);
    if (jurisdictions) {
      analysis.extractedData.jurisdiction = jurisdictions[0];
    }
  }

  return analysis;
}

// Basic one-shot chat with system prompt for conversational behavior
const chatSchema = z.object({
  prompt: z.string().min(1),
  assistanceType: z.enum(['Draft Contract', 'Suggest Clause', 'Legal Assistance', 'General Legal Query']).optional(),
  tone: z.enum(['Professional', 'Casual', 'Friendly', 'Formal']).optional()
});
// Optional auth for doc generation to keep local/dev smooth
router.post('/chat', maybeAuth, async (req: AuthRequest, res) => {
  const parsed = chatSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    // Use createChatSession with dynamic system prompt based on assistance type and tone
    const chat = createChatSession(parsed.data.assistanceType, parsed.data.tone);
    const result = await chat.sendMessage({ message: parsed.data.prompt });
    return res.json({ text: (result.text || '').trim() });
  } catch (e: unknown) {
    const errorMsg = e instanceof Error ? e.message : 'AI request failed';
    return res.status(500).json({ error: errorMsg });
  }
});

// Enhanced conversational chat endpoint with status streaming
const conversationalChatSchema = z.object({
  prompt: z.string().min(1),
  conversationHistory: z.array(z.object({
    role: z.enum(['user', 'model']),
    content: z.string()
  })).optional(),
  stage: z.string().optional(),
  collectedData: z.record(z.any()).optional(),
  assistanceType: z.enum(['Draft Contract', 'Suggest Clause', 'Legal Assistance', 'General Legal Query']).optional(),
  tone: z.enum(['Professional', 'Casual', 'Friendly', 'Formal']).optional()
});

// New immediate generation endpoint with progress streaming
const immediateGenerationSchema = z.object({
  prompt: z.string().min(1),
  assistanceType: z.enum(['Draft Contract', 'Suggest Clause', 'Legal Assistance', 'General Legal Query']).optional(),
  tone: z.enum(['Professional', 'Casual', 'Friendly', 'Formal']).optional()
});

router.post('/chat/immediate', maybeAuth, async (req: AuthRequest, res) => {
  const parsed = immediateGenerationSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: parsed.error.message });
  }

  const { prompt, assistanceType = 'Draft Contract', tone = 'Professional' } = parsed.data;

  try {
    const ai = getAI();
    if (!ai) {
      return res.status(500).json({ error: 'AI not configured' });
    }

    // Create system prompt for immediate generation with intelligent defaults
    const immediateSystemPrompt = `You are Lexi, an expert AI legal assistant specializing in immediate document generation.

IMMEDIATE GENERATION MODE: Generate legal documents directly without asking clarifying questions.

INSTRUCTIONS:
- Use intelligent defaults and legal best practices when specific details aren't provided
- Apply standard terms appropriate for the document type
- Include common protective clauses and industry standards
- Use placeholder text like "[Party A]", "[Party B]", "[Jurisdiction]" where specific details are needed
- Generate comprehensive, professional documents that users can customize later
- Always include proper legal disclaimers and recommendations for legal review

ASSISTANCE TYPE: ${assistanceType}
TONE: ${tone}

Generate the requested document immediately using best practices and intelligent defaults.`;

    const chat = ai.chats.create({
      model: 'gemini-2.5-flash',
      config: {
        systemInstruction: immediateSystemPrompt,
      },
    });

    const result = await chat.sendMessage({ message: prompt });
    const response = (result.text || '').trim();

    return res.json({
      text: response,
      stage: 'completed',
      messageType: 'document'
    });

  } catch (e: unknown) {
    const errorMsg = e instanceof Error ? e.message : 'AI request failed';
    return res.status(500).json({ error: errorMsg });
  }
});

router.post('/chat/conversational', maybeAuth, async (req: AuthRequest, res) => {
  const parsed = conversationalChatSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: parsed.error.message });
  }

  const { prompt, conversationHistory = [], stage = 'initial', collectedData = {}, assistanceType, tone } = parsed.data;

  try {
    const ai = getAI();
    if (!ai) {
      return res.status(500).json({ error: 'AI not configured' });
    }

    // Create enhanced system prompt for conversational flow
    let conversationalSystemPrompt = `You are Lexi, an expert AI legal assistant. You engage in multi-turn conversations to gather requirements before generating legal documents.

STRICT SCOPE RESTRICTIONS:
- You MUST refuse any requests outside of legal/contract assistance
- You CANNOT help with: coding, math, general knowledge, entertainment, personal advice, or any non-legal topics
- If asked about non-legal topics, politely redirect: "I'm specialized in legal assistance only. I can help you with contracts, legal documents, clause suggestions, or legal guidance. How can I assist you with your legal needs?"

CURRENT CONVERSATION STAGE: ${stage}
COLLECTED DATA: ${JSON.stringify(collectedData)}`;

    // Add assistance type specific instructions
    if (assistanceType) {
      conversationalSystemPrompt += `\n\nCURRENT ASSISTANCE TYPE: ${assistanceType}`;
      switch (assistanceType) {
        case 'Draft Contract':
          conversationalSystemPrompt += '\nFocus on comprehensive document creation with detailed clauses and legal structure.';
          break;
        case 'Suggest Clause':
          conversationalSystemPrompt += '\nProvide specific clause recommendations with explanations and legal rationale.';
          break;
        case 'Legal Assistance':
          conversationalSystemPrompt += '\nOffer general legal guidance, explanations, and educational information.';
          break;
        case 'General Legal Query':
          conversationalSystemPrompt += '\nAnswer legal questions with clear explanations and practical guidance.';
          break;
      }
    }

    // Add tone specific instructions
    if (tone) {
      conversationalSystemPrompt += `\n\nCURRENT TONE: ${tone}`;
      switch (tone) {
        case 'Professional':
          conversationalSystemPrompt += '\nUse formal legal language, precise terminology, and structured responses.';
          break;
        case 'Casual':
          conversationalSystemPrompt += '\nUse conversational language while maintaining accuracy, with a friendly approach.';
          break;
        case 'Friendly':
          conversationalSystemPrompt += '\nUse warm, approachable tone with encouragement and supportive language.';
          break;
        case 'Formal':
          conversationalSystemPrompt += '\nUse highly structured, traditional legal communication style with formal language.';
          break;
      }
    }

    conversationalSystemPrompt += `

CONVERSATION FLOW:
1. INITIAL: Detect contract requests and ask clarifying questions
2. GATHERING_REQUIREMENTS: Ask specific questions about parties, jurisdiction, terms
3. CONFIRMING_DETAILS: Summarize and confirm collected information
4. GENERATING_CONTRACT: Create the legal document with progress updates

RESPONSE FORMATS:
- For questions: Use clear, numbered questions with specific requirements
- For confirmations: Summarize all collected information clearly
- For contracts: Use the existing ---CONTRACT START--- and ---CONTRACT END--- markers

Always be conversational, helpful, and thorough in gathering requirements.`;

    const chat = ai.chats.create({
      model: 'gemini-2.5-flash',
      config: {
        systemInstruction: conversationalSystemPrompt,
      },
    });

    // Add conversation history to context
    for (const msg of conversationHistory) {
      await chat.sendMessage({ message: `${msg.role}: ${msg.content}` });
    }

    // Send the current prompt
    const result = await chat.sendMessage({ message: prompt });
    const response = (result.text || '').trim();

    // Analyze response to determine next stage and extract data
    const responseAnalysis = analyzeConversationalResponse(response, stage, collectedData);

    return res.json({
      text: response,
      nextStage: responseAnalysis.nextStage,
      extractedData: responseAnalysis.extractedData,
      messageType: responseAnalysis.messageType,
      suggestedResponses: responseAnalysis.suggestedResponses,
      requiresConfirmation: responseAnalysis.requiresConfirmation
    });

  } catch (e: unknown) {
    const errorMsg = e instanceof Error ? e.message : 'AI request failed';
    return res.status(500).json({ error: errorMsg });
  }
});

// Streaming conversational chat with status updates
router.get('/chat/conversational/stream', maybeAuth, async (req: AuthRequest, res) => {
  const prompt = (req.query.prompt as string) || '';
  const stage = (req.query.stage as string) || 'initial';
  const collectedDataStr = (req.query.collectedData as string) || '{}';

  if (!prompt.trim()) {
    return res.status(400).end();
  }

  const ai = getAI();
  if (!ai) {
    return res.status(500).end();
  }

  // Set up SSE headers
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache, no-transform');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no');
  res.flushHeaders?.();

  let ended = false;
  req.on('close', () => { ended = true; });

  try {
    let collectedData = {};
    try {
      collectedData = JSON.parse(collectedDataStr);
    } catch {
      // Invalid JSON, use empty object
    }

    // Send initial status
    if (!ended) {
      res.write(`data: ${JSON.stringify({
        type: 'status',
        content: 'Analyzing your request...',
        statusType: 'thinking'
      })}\n\n`);
    }

    // Simulate processing time with status updates
    const statusUpdates = [
      { content: 'Understanding your requirements...', statusType: 'thinking', delay: 1500 },
      { content: 'Preparing response...', statusType: 'researching', delay: 1500 }
    ];

    for (const update of statusUpdates) {
      if (ended) break;

      await new Promise(resolve => setTimeout(resolve, update.delay));

      if (!ended) {
        res.write(`data: ${JSON.stringify({
          type: 'status',
          content: update.content,
          statusType: update.statusType
        })}\n\n`);
      }
    }

    // Generate the actual response
    const conversationalSystemPrompt = `You are Lexi, an expert AI legal assistant. You engage in multi-turn conversations to gather requirements before generating legal documents.

CURRENT CONVERSATION STAGE: ${stage}
COLLECTED DATA: ${JSON.stringify(collectedData)}

CONVERSATION FLOW:
1. INITIAL: Detect contract requests and ask clarifying questions
2. GATHERING_REQUIREMENTS: Ask specific questions about parties, jurisdiction, terms
3. CONFIRMING_DETAILS: Summarize and confirm collected information
4. GENERATING_CONTRACT: Create the legal document with progress updates

Always be conversational, helpful, and thorough in gathering requirements.`;

    const chat = ai.chats.create({
      model: 'gemini-2.5-flash',
      config: {
        systemInstruction: conversationalSystemPrompt,
      },
    });

    const result = await chat.sendMessage({ message: prompt });
    const response = (result.text || '').trim();

    if (!ended) {
      // Send the final response
      const responseAnalysis = analyzeConversationalResponse(response, stage, collectedData);

      res.write(`data: ${JSON.stringify({
        type: 'response',
        text: response,
        nextStage: responseAnalysis.nextStage,
        extractedData: responseAnalysis.extractedData,
        messageType: responseAnalysis.messageType,
        suggestedResponses: responseAnalysis.suggestedResponses,
        requiresConfirmation: responseAnalysis.requiresConfirmation
      })}\n\n`);

      res.write(`event: done\n`);
      res.write(`data: {}\n\n`);
    }

    res.end();

  } catch (e: unknown) {
    if (!ended) {
      const errorMsg = e instanceof Error ? e.message : 'AI request failed';
      res.write(`event: error\n`);
      res.write(`data: ${JSON.stringify({ error: errorMsg })}\n\n`);
    }
    res.end();
  }
});

// SSE streaming chat
router.get('/chat/stream', maybeAuth, async (req: AuthRequest, res) => {
  const prompt = (req.query.prompt as string) || '';
  if (!prompt.trim()) {return res.status(400).end();}
  const ai = getAI();
  if (!ai) {return res.status(500).end();}
  res.setHeader('Content-Type', 'text/event-stream');
  // Prevent proxy buffering and transformations so tokens flush immediately
  res.setHeader('Cache-Control', 'no-cache, no-transform');
  res.setHeader('Connection', 'keep-alive');
  // Nginx accel-buffering hint (ignored by some proxies, harmless otherwise)
  res.setHeader('X-Accel-Buffering', 'no');
  res.flushHeaders?.();
  // Prime the stream to defeat certain proxy buffers
  try { res.write(':' + ' '.repeat(2048) + '\n\n'); } catch { /* Ignore write errors for stream priming */ }
  let ended = false;
  res.on('close', () => {
    ended = true;
    try { res.end(); } catch { /* Ignore errors when ending response stream */ }
  });
  try {
    const chat = ai.chats.create({ model: 'gemini-2.5-flash' });
    const result = await chat.sendMessageStream({ message: prompt });
    for await (const chunk of result as AsyncIterable<{ text?: string }>) {
      if (ended) {break;}
      const txt = (chunk?.text || '').toString();
      if (txt) {
        res.write(`data: ${JSON.stringify({ text: txt })}\n\n`);
      }
    }
    res.write(`event: done\n`);
    res.write(`data: {}\n\n`);
    res.end();
  } catch (e: unknown) {
    if (e instanceof Error) {
      res.status(500).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Unknown error' });
    }
    try { res.write(`event: error\n`); res.write(`data: ${JSON.stringify({ error: 'stream-failed' })}\n\n`); } catch { /* Ignore write errors during error handling */ }
    res.end();
  }
});

// Compare versions -> HTML
const compareSchema = z.object({ contentA: z.string(), contentB: z.string() });
router.post('/compare-versions', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = compareSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `You are a document comparison tool. Compare the two versions of the document provided below. Your goal is to produce a single HTML document that shows the changes from "Version A" to "Version B". Use standard HTML <ins> tags for additions and <del> tags for deletions. Do not add any explanation or commentary outside of the HTML document itself.
\n--- VERSION A ---\n${parsed.data.contentA}\n--- END VERSION A ---\n\n--- VERSION B ---\n${parsed.data.contentB}\n--- END VERSION B ---`,
    });
      // Try to parse the response as JSON and check for required properties
      let html = null;
      try {
        html = typeof response.text === 'string' ? response.text.trim() : null;
      } catch { /* Ignore JSON parsing errors */ }
      if (!html) {
        return res.status(500).json({ error: 'AI request failed' });
      }
      return res.json({ html });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

// Analyze document
const analyzeSchema = z.object({ content: z.string() });
router.post('/analyze', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `Analyze the following legal document. Provide a brief overall summary and a list of findings. Findings should include potential issues, unclear language, or missing clauses. For each finding, provide a severity ('High Priority', 'Suggestion', or 'Information') and a description of the issue. Document content:\n\n${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            summary: { type: Type.STRING },
            findings: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  severity: { type: Type.STRING },
                  description: { type: Type.STRING },
                },
              },
            },
          },
        },
      },
    });
    // Try to parse the response as JSON and check for required properties
    let parsedJson: { summary?: string; findings?: unknown[] } | null = null;
    try {
      parsedJson = typeof response.text === 'string' ? JSON.parse(response.text) : null;
    } catch { /* Ignore JSON parsing errors */ }
    if (!parsedJson || typeof parsedJson.summary !== 'string' || !Array.isArray(parsedJson.findings)) {
      return res.status(500).json({ error: 'AI request failed' });
    }
    return res.json({ summary: parsedJson.summary, findings: parsedJson.findings });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }

});

router.post('/external-analyze', requireAuth, async (req: AuthRequest, res) => {
  const key = (process.env.GEMINI_API_KEY || process.env.VITE_GEMINI_API_KEY || '').trim();
  if (!key || key === 'undefined') {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const result = await analyzeExternalDocument(parsed.data.content);
    return res.json({ result });
  } catch (e: unknown) {
    const errorMsg = e instanceof Error ? e.message : 'AI request failed';
    if (errorMsg.toLowerCase().includes('not configured')) {
      return res.status(500).json({ error: 'AI not configured' });
    }
    return res.status(500).json({ error: errorMsg || 'AI request failed' });
  }
});

// Suggest clauses
router.post('/suggest-clauses', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `Based on the following legal document, suggest 3-5 relevant clauses that could be added to improve it. Provide a title, a brief description of what the clause does, and the full legal text for the clause. Here is the document content: \n\n${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            suggestions: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  title: { type: Type.STRING },
                  description: { type: Type.STRING },
                  text: { type: Type.STRING },
                },
              },
            },
          },
        },
      },
    });
    const jsonStr = (response.text || '').trim();
    const parsedJson = JSON.parse(jsonStr);
    return res.json({ suggestions: parsedJson.suggestions || [] });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

// Extract key dates
router.post('/extract-key-dates', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `From the following document, extract any key dates such as 'Effective Date', 'End Date', 'Termination Date', or 'Renewal Notice Date'. Return in JSON with an array 'keyDates' of objects: { event: string, date: string (YYYY-MM-DD) }. Content: ${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: { keyDates: { type: Type.ARRAY, items: { type: Type.OBJECT, properties: { event: { type: Type.STRING }, date: { type: Type.STRING } } } } },
        },
      },
    });
    const jsonStr = (response.text || '').trim();
    const parsedJson = JSON.parse(jsonStr);
    return res.json({ keyDates: parsedJson.keyDates || [] });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

// Extract obligations
router.post('/extract-obligations', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `From the following document, extract obligations as { description, dueDate (YYYY-MM-DD) } in JSON array 'obligations'. Only include obligations with a parseable due date. Content: ${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: { obligations: { type: Type.ARRAY, items: { type: Type.OBJECT, properties: { description: { type: Type.STRING }, dueDate: { type: Type.STRING } } } } },
        },
      },
    });
    const jsonStr = (response.text || '').trim();
    const parsedJson = JSON.parse(jsonStr);
    return res.json({ obligations: parsedJson.obligations || [] });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

export default router;
