import React from 'react';
import { cn } from '../lib/utils';
import { BotIcon, CheckIcon, EditIcon } from './Icons';
import MarkdownRenderer from './MarkdownRenderer';

interface InteractiveMessageProps {
  content: string;
  suggestedResponses?: string[];
  requiresConfirmation?: boolean;
  conversationData?: Record<string, any>;
  onResponseClick?: (response: string) => void;
  onConfirm?: () => void;
  onModify?: () => void;
  className?: string;
}

const InteractiveMessage: React.FC<InteractiveMessageProps> = ({
  content,
  suggestedResponses,
  requiresConfirmation,
  conversationData,
  onResponseClick,
  onConfirm,
  onModify,
  className
}) => {
  return (
    <div className={cn('flex items-start gap-4', className)}>
      {/* Bot avatar */}
      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
        <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
      </div>
      
      {/* Message content */}
      <div className="flex-1 min-w-0">
        <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
          <MarkdownRenderer content={content} />
        </div>
        
        {/* Conversation data summary */}
        {conversationData && Object.keys(conversationData).length > 0 && (
          <div className="mt-3 p-3 bg-brand-50 dark:bg-brand-900/20 rounded-lg border border-brand-200 dark:border-brand-800">
            <h4 className="text-sm font-medium text-brand-700 dark:text-brand-400 mb-2">
              Collected Information:
            </h4>
            <div className="space-y-1">
              {Object.entries(conversationData).map(([key, value]) => (
                <div key={key} className="flex justify-between text-sm">
                  <span className="text-zinc-600 dark:text-zinc-400 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}:
                  </span>
                  <span className="text-zinc-800 dark:text-zinc-200 font-medium">
                    {Array.isArray(value) ? value.join(', ') : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Suggested responses */}
        {suggestedResponses && suggestedResponses.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {suggestedResponses.map((response, index) => (
              <button
                key={index}
                onClick={() => onResponseClick?.(response)}
                className="px-3 py-2 text-sm bg-white dark:bg-zinc-700 border border-zinc-300 dark:border-zinc-600 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-600 transition-colors"
              >
                {response}
              </button>
            ))}
          </div>
        )}
        
        {/* Confirmation buttons */}
        {requiresConfirmation && (
          <div className="mt-3 flex gap-2">
            <button
              onClick={onConfirm}
              className="flex items-center gap-2 px-4 py-2 bg-brand-600 text-white rounded-lg hover:bg-brand-700 transition-colors"
            >
              <CheckIcon className="w-4 h-4" />
              Continue
            </button>
            <button
              onClick={onModify}
              className="flex items-center gap-2 px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-colors"
            >
              <EditIcon className="w-4 h-4" />
              Modify
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default InteractiveMessage;
