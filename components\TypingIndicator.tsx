import React from 'react';
import { cn } from '../lib/utils';
import { BotIcon } from './Icons';

interface TypingIndicatorProps {
  className?: string;
  message?: string;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ 
  className, 
  message = "Lexi is typing..." 
}) => {
  return (
    <div className={cn('flex items-start gap-4', className)}>
      {/* Bot avatar */}
      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
        <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
      </div>
      
      {/* Typing indicator bubble */}
      <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 max-w-xs">
        <div className="flex items-center gap-3">
          {/* Animated typing dots */}
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce"></div>
          </div>
          
          {/* Optional typing message */}
          {message && (
            <span className="text-sm text-zinc-500 dark:text-zinc-400">
              {message}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;
